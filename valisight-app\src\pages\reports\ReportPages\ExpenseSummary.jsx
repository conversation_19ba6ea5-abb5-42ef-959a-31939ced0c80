import { useEffect, useRef } from "react";
import ApexCharts from "apexcharts";

const ExpenseSummaryDashboard = ({
  headerTextStyle = {},
  subHeadingTextStyle = {},
  contentTextStyle = {},
  reportData = null,
  contentSettings = null, // Add contentSettings prop
}) => {
  const roaRoeRef = useRef(null);
  const expensesPieRef = useRef(null);
  const expensesMonthlyRef = useRef(null);
  const wagesRevenueRef = useRef(null);

  // Store chart instances for export functionality
  const roaRoeChartRef = useRef(null);
  const expensesPieChartRef = useRef(null);
  const expensesMonthlyChartRef = useRef(null);
  const wagesRevenueChartRef = useRef(null);

  // Helper function to sort data with "Other" last
  const sortDataWithOtherLast = (data) => {
    return data.sort((a, b) => {
      const aIsOther = (a.account_name || '').toLowerCase().includes('other');
      const bIsOther = (b.account_name || '').toLowerCase().includes('other');

      if (aIsOther && !bIsOther) return 1;  // a (Other) goes after b
      if (!aIsOther && bIsOther) return -1; // b (Other) goes after a
      return 0; // maintain original order for non-Other items
    });
  };

  // Function to check if a chart should be displayed based on content settings
  const shouldDisplayChart = (chartKey) => {
    if (!contentSettings?.chartSettings) return true; // Default to true if no settings
    return contentSettings.chartSettings[chartKey] === true;
  };

  // Enhanced data validation function
  const isDataLoaded = () => {
    if (!reportData) {
      return false;
    }

    // Check if at least some required data exists - make it more flexible
    const hasRoeRoaData = reportData.roeRoa &&
      Array.isArray(reportData.roeRoa) &&
      reportData.roeRoa.length > 0;

    const hasExpensesData = reportData.expensesTopAccounts &&
      Array.isArray(reportData.expensesTopAccounts) &&
      reportData.expensesTopAccounts.length > 0;

    // For monthly expenses, check for new detailed breakdown data first, then fallback to performance data
    const hasMonthlyExpensesData = reportData.expensesTopAccountsMonthly &&
      Array.isArray(reportData.expensesTopAccountsMonthly) &&
      reportData.expensesTopAccountsMonthly.length > 0;

    const hasMonthlyData = hasMonthlyExpensesData ||
      (reportData.monthlyPerformanceBreakDown &&
        Array.isArray(reportData.monthlyPerformanceBreakDown) &&
        reportData.monthlyPerformanceBreakDown.length > 0);


    // Return true if we have at least some data to work with
    return hasRoeRoaData || hasExpensesData || hasMonthlyData;
  };

  useEffect(() => {
    if (isDataLoaded()) {
      initializeCharts();
    }
  }, [reportData, contentSettings]); // Add contentSettings to dependency array

  // Helper function to format month-year
  const formatMonthYear = (year, month) => {
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;
  };

  // Helper function to format currency values with appropriate units
  const formatCurrency = (val, options = {}) => {
    if (val === null || val === undefined || isNaN(val) || val === 0) {
      return options.showZero ? '$0' : '';
    }

    const absVal = Math.abs(val);

    if (absVal >= 1000000) {
      // Trillions
      return '$' + (val / 1000000).toFixed(1) + 't';
    } else if (absVal >= 1000) {
      // Millions
      return '$' + (val / 1000).toFixed(1) + 'm';
    } else {
      // Thousands (since our data is already in thousands)
      return '$' + val.toFixed(2) + 'k';
    }
  };

  // Process ROE/ROA data from API
  const processRoeRoaData = () => {
    if (!reportData?.roeRoa)
      return { roaData: [], roeData: [], categories: [] };

    const roaData = reportData.roeRoa.map((item) => {
      const value = parseFloat(item.roa || 0);
      return isNaN(value) ? 0 : value;
    });
    const roeData = reportData.roeRoa.map((item) => {
      const value = parseFloat(item.roe || 0);
      return isNaN(value) ? 0 : value;
    });


    // Create categories from the data if available
    const categories = reportData.roeRoa.map((item) => {
      if (item.year && item.month) {
        return formatMonthYear(item.year, item.month);
      }
      return item.period || `Period ${reportData.roeRoa.indexOf(item) + 1}`;
    });

    return { roaData, roeData, categories };
  };

  // Process expenses pie chart data from API
  const processExpensesPieData = () => {
    if (!reportData?.expensesTopAccounts) return { data: [], labels: [] };

    // Sort data with "Other" last
    const sortedExpenses = sortDataWithOtherLast([...reportData.expensesTopAccounts]);

    const data = sortedExpenses.map((item) => {
      const value = parseFloat(item.total_expense || 0);
      return isNaN(value) ? 0 : value / 1000; // Convert to thousands
    });

    const labels = sortedExpenses.map((item) => {
      const expense = parseFloat(item.total_expense || 0);
      const percentage = parseFloat(item.percentage_of_total || 0);
      const expenseDisplay = isNaN(expense) ? "0.0" : (expense / 1000).toFixed(2); // Fixed to 1 decimal place
      const percentageDisplay = isNaN(percentage) ? "0.0" : percentage.toFixed(2); // Also fixed percentage to 1 decimal

      return `${item.account_name || "Unknown"
        } ${expenseDisplay}k (${percentageDisplay}%)`;
    });

    return { data, labels };
  };

  // Process monthly expenses data from API - use expensesTopAccountsMonthly for detailed account breakdown
  const processMonthlyExpensesData = () => {
    // Check if we have the new monthly expenses breakdown data
    if (reportData?.expensesTopAccountsMonthly && Array.isArray(reportData.expensesTopAccountsMonthly)) {
      return processDetailedMonthlyExpenses();
    }

    // Fallback to old method if new data is not available
    if (!reportData?.monthlyPerformanceBreakDown) return { series: [], categories: [] };

    // Get monthly data and create categories
    const monthlyData = reportData.monthlyPerformanceBreakDown;
    const categories = monthlyData.map(item =>
      formatMonthYear(item.year, item.month)
    );

    // Since we don't have individual account breakdowns by month,
    // we'll create a single series for total expenses
    const totalExpensesData = monthlyData.map(item => {
      const expense = parseFloat(item.totalExpenses || 0) / 1000; // Convert to thousands
      return isNaN(expense) ? 0 : expense;
    });

    const series = [{
      name: 'Total Expenses',
      data: totalExpensesData
    }];

    return { series, categories };
  };

  // Process detailed monthly expenses breakdown by account
  const processDetailedMonthlyExpenses = () => {
    if (
      !reportData?.expensesTopAccountsMonthly ||
      !Array.isArray(reportData.expensesTopAccountsMonthly)
    ) {
      return { series: [], categories: [] };
    }

    // The backend now returns normalized data (one row per account per month)
    // We need to transform it into the format expected by the chart

    // First, get all unique months and sort them chronologically
    const monthsSet = new Set();
    reportData.expensesTopAccountsMonthly.forEach((row) => {
      if (row.year && row.month) {
        monthsSet.add(`${row.year}-${row.month.toString().padStart(2, "0")}`);
      }
    });

    const sortedMonths = Array.from(monthsSet).sort();

    // Create categories (month labels) from the sorted months
    const categories = sortedMonths.map((monthKey) => {
      const [year, month] = monthKey.split("-");
      return formatMonthYear(parseInt(year), parseInt(month));
    });

    // Group data by account
    const accountData = {};
    reportData.expensesTopAccountsMonthly.forEach((row) => {
      const accountName = row.account_name || "Unknown";
      const monthKey = `${row.year}-${row.month.toString().padStart(2, "0")}`;

      if (!accountData[accountName]) {
        accountData[accountName] = {
          name: accountName,
          total_expense: row.total_expense || 0,
          monthlyData: {},
        };
      }

      accountData[accountName].monthlyData[monthKey] =
        parseFloat(row.monthly_expense || 0) / 1000; // Convert to thousands
    });

    // Convert to array and sort with "Other" last
    const accountsArray = Object.values(accountData);
    const sortedAccounts = accountsArray.sort((a, b) => {
      if (a.name === "Other") return 1;
      if (b.name === "Other") return -1;
      return b.total_expense - a.total_expense;
    });

    // Create series for each account
    const series = sortedAccounts.map((account) => {
      const data = sortedMonths.map((monthKey) => {
        const value = account.monthlyData[monthKey] || 0;
        return isNaN(value) ? 0 : value;
      });

      return {
        name: account.name,
        data: data,
      };
    });

    return { series, categories };
  };

  // Process wages vs revenue data from monthly performance data
  const processWagesRevenueData = () => {
    // Check if we have the new wagesRevenueMonthWise data
    if (reportData?.wagesRevenueMonthWise && Array.isArray(reportData.wagesRevenueMonthWise)) {
      const wagesData = reportData.wagesRevenueMonthWise;

      const income = wagesData.map((item) => {
        const value = parseFloat(item.income || 0) / 1000; // Convert to thousands for better display
        return isNaN(value) ? 0 : value;
      });

      // Use the actual ga_salaries and sales_salaries from the API
      const salariesGA = wagesData.map((item) => {
        const value = parseFloat(item.ga_salaries || 0) / 1000; // Convert to thousands for better display
        return isNaN(value) ? 0 : value;
      });

      const salariesSales = wagesData.map((item) => {
        const value = parseFloat(item.sales_salaries || 0) / 1000; // Convert to thousands for better display
        return isNaN(value) ? 0 : value;
      });

      const categories = wagesData.map((item) =>
        formatMonthYear(item.year, item.month)
      );

      return { income, salariesGA, salariesSales, categories };
    }

    // Fallback to old method if new data is not available
    if (!reportData?.monthlyPerformanceBreakDown)
      return { income: [], salariesGA: [], salariesSales: [], categories: [] };

    const monthlyData = reportData.monthlyPerformanceBreakDown;

    const income = monthlyData.map((item) => {
      const value = parseFloat(item.totalIncome || 0) / 1000000; // Convert to millions
      return isNaN(value) ? 0 : value;
    });

    const salariesGA = monthlyData.map((item) => {
      const expenses = parseFloat(item.totalExpenses || 0);
      const value = (expenses * 0.3) / 1000000; // Convert to millions
      return isNaN(value) ? 0 : value;
    });

    const salariesSales = monthlyData.map((item) => {
      const expenses = parseFloat(item.totalExpenses || 0);
      const value = (expenses * 0.2) / 1000000; // Convert to millions
      return isNaN(value) ? 0 : value;
    });

    const categories = monthlyData.map((item) =>
      formatMonthYear(item.year, item.month)
    );

    return { income, salariesGA, salariesSales, categories };
  };


  const formatWagesRevenueValue = (val) => {
    if (val === null || val === undefined || isNaN(val) || val === 0) return "0";

    const absVal = Math.abs(val);

    if (absVal >= 1000) {
      return (val / 1000).toFixed(1) + 'm';
    } else if (absVal >= 1) {
      return val.toFixed(2) + 'k';
    } else {
      return (val * 1000).toFixed(0); // Convert back to original value for small numbers
    }
  };

  const initializeCharts = () => {
    const {
      roaData,
      roeData,
      categories: roaRoeCategories,
    } = processRoeRoaData();
    const { data: pieData, labels: pieLabels } = processExpensesPieData();
    const { series: monthlyExpensesSeries, categories: monthlyCategories } =
      processMonthlyExpensesData();
    const {
      income,
      salariesGA,
      salariesSales,
      categories: wagesCategories,
    } = processWagesRevenueData();

    // Chart colors
    const colors = {
      roaRoe: ["#4a90e2", "#ff6b47"],
      expensesPie: [
        "#1f4e79",
        "#20b2aa",
        "#ff7f50",
        "#4db6ac",
        "#95a5a6",
        "#5d6d7e",
        "#bdc3c7",
        "#ffab91",
        "#9575cd",
        "#ba68c8",
        "#90a4ae",
      ],
      monthlyExpenses: [
        "#1f4e79",
        "#20b2aa",
        "#ff7f50",
        "#4db6ac",
        "#95a5a6",
        "#5d6d7e",
        "#bdc3c7",
        "#ffab91",
        "#9575cd",
        "#ba68c8",
        "#90a4ae",
        "#4a6fa5",
        "#2d5f5f",
        "#5f9ea0",
        "#8b7d82",
        "#4682b4",
        "#b0c4de",
        "#dda0dd",
        "#87ceeb",
        "#f0e68c",
        "#d3d3d3",
      ],
      wagesRevenue: ["#20b2aa", "#4a4a9a", "#ff7f50"],
    };

    // 1. ROA and ROE Line Chart
    const roaRoeOptions = {
      series: [
        { name: "ROA", data: roaData },
        { name: "ROE", data: roeData },
      ],
      chart: {
        type: "line",
        height: 250, // reduced height
        toolbar: { show: false }, // removes menu like download, zoom, etc.
        background: "transparent",
        zoom: {
          enabled: false
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val, opts) {
          if (val === null || val === undefined || isNaN(val)) return "0";
          return val + "%";
        },
        style: {
          fontSize: "14px",
          colors: ["#333"],
          fontWeight: "500",
        },
        background: { enabled: false },
        offsetY: 0, // default (we override below in dropShadow trick)
      },
      stroke: {
        curve: "smooth",
        width: 2,
      },
      xaxis: {
        categories: roaRoeCategories,
        labels: {
          style: {
            colors: "#666",
            fontSize: "14px",
          },
          offsetY: 8 // move months slightly lower (positive value pushes down)
        },
        axisBorder: { show: false },
        axisTicks: { show: false },
      },

      yaxis: { show: false },
      colors: colors.roaRoe,
      markers: {
        size: 4,
        strokeColors: "#fff",
        strokeWidth: 1,
        hover: { size: 6 },
      },
      legend: {
        position: "bottom",
        horizontalAlign: "center",
        offsetY: 20, // pushes legend further down
        markers: { width: 8, height: 8, radius: 4 },
        labels: {
          colors: ["#333"],
          useSeriesColors: false,
          fontSize: "14px",
        },
        itemMargin: { horizontal: 20, vertical: 0 },
      },
      tooltip: {
        y: {
          formatter: function (val) {
            if (val === null || val === undefined || isNaN(val)) return "0";
            return val + "%";
          },
        },
      },
      grid: {
        show: false,
        padding: { left: 25, right: 25, top: 20, bottom: 0 },
      },
      annotations: {
        yaxis: [
          {
            y: 0,
            borderColor: "#999",
            borderWidth: 1,
            strokeDashArray: 0,
            opacity: 1,
          },
        ],
      },
      // ✅ Directly apply per-series offset here
      dataLabels: {
        enabled: true,
        formatter: function (val, opts) {
          if (val === null || val === undefined || isNaN(val)) return "0";
          const offsets = [-10, 10]; // ROA up, ROE down
          this.offsetY = offsets[opts.seriesIndex];
          return val + "%";
        },
        style: {
          fontSize: "14px",
          colors: ["#333"],
          fontWeight: "500",
        },
        background: { enabled: false },
      },
    };


    // 2. Expenses Pie Chart
    const expensesPieOptions = {
      series: pieData,
      chart: {
        type: "pie",
        height: 400,
        toolbar: { show: false },
        zoom: {
          enabled: false
        }
      },
      labels: pieLabels,
      colors: colors.expensesPie,
      dataLabels: {
        enabled: true,
        formatter: function (val, opts) {
          if (!opts || !opts.w || !opts.w.globals || !opts.w.globals.series)
            return "";
          const value = opts.w.globals.series[opts.seriesIndex];
          return value.toFixed(1) + 'k'; // Fixed to 1 decimal place with 'k' suffix
        },
        style: {
          fontSize: "14px",
          colors: ["#fff"],
          fontWeight: "500",
        },
        dropShadow: {
          enabled: false,
        },
      },
      legend: {
        position: "right",
        fontSize: "14px",
        fontWeight: "400",
        markers: {
          width: 10,
          height: 10,
          radius: 5,
        },
        labels: {
          colors: "#333",
          useSeriesColors: false,
          fontFamily: "Calibri",
        },
        itemMargin: {
          horizontal: 5,
          vertical: 3,
        },
        offsetX: 0,
      },
      plotOptions: {
        pie: {
          dataLabels: {
            offset: 0,
          },
        },
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return '$' + val.toFixed(1) + 'k'; // Fixed to 1 decimal place in tooltip too
          },
        },
      },
      stroke: {
        show: false,
      },
      responsive: [
        {
          breakpoint: 768,
          options: {
            legend: { position: "bottom" },
          },
        },
      ],
    };

    // 3. Monthly Expenses Stacked Chart
    const expensesMonthlyOptions = {
      series: monthlyExpensesSeries,
      chart: {
        type: "bar",
        height: 450,
        stacked: true,
        toolbar: { show: false },
        background: "transparent",
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: "60%",
          dataLabels: {
            total: {
              enabled: true,
              offsetY: -25,
              style: {
                fontSize: "12px",
                fontWeight: "600",
                color: "#333",
              },
              formatter: function (val) {
                return formatCurrency(val);
              },
            },
          },
        },
      },
      dataLabels: {
        enabled: false,
        formatter: function (val) {
          return formatCurrency(val);
        },
        style: {
          fontSize: "12px",
          fontWeight: "500",
          colors: ["#333"],
        },
        offsetY: -5,
        background: {
          enabled: false,
        },
        dropShadow: {
          enabled: false,
        },
      },
      xaxis: {
        categories: monthlyCategories,
        labels: {
          style: {
            colors: "#666",
            fontSize: "12px",
          },
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
      },
      yaxis: {
        show: false,
      },
      colors: colors.monthlyExpenses,
      legend: {
        position: "bottom",
        fontSize: "12px",
        fontWeight: "400",
        markers: {
          width: 8,
          height: 8,
          radius: 4,
        },
        labels: {
          colors: "#333",
          useSeriesColors: true,
        },
        itemMargin: {
          horizontal: 8,
          vertical: 3,
        },
        offsetY: 10,
        onItemClick: {
          toggleDataSeries: true,
        },
        onItemHover: {
          highlightDataSeries: true,
        },
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return formatCurrency(val, { showZero: true });
          },
        },
      },
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 20,
          bottom: 0,
        },
      },
      stroke: {
        show: false,
      },
    };

    // 4. Wages vs Revenue Chart (using real data from API)
    const wagesRevenueOptions = {
      series: [
        {
          name: "Income",
          type: "line",
          data: income,
        },
        {
          name: "Salaries - G&A",
          type: "column",
          data: salariesGA,
        },
        {
          name: "Salaries - Sales",
          type: "column",
          data: salariesSales,
        },
      ],
      chart: {
        height: 450,
        type: "line",
        stacked: true,
        toolbar: { show: false },
        background: "transparent",
        zoom: {
          enabled: false
        }
      },
      dataLabels: {
        enabled: true,
        enabledOnSeries: [0], // Only show labels on Income line (series 0)
        formatter: function (val) {
          if (val === null || val === undefined || isNaN(val)) return "";
          return "$" + formatWagesRevenueValue(val);
        },
        style: {
          fontSize: "14px",
          colors: ["#20b2aa"], // Teal color for Income line
          fontWeight: "500",
        },
        offsetY: -10,
        background: {
          enabled: false,
        },
        dropShadow: {
          enabled: false,
        },
      },
      stroke: {
        width: [2, 0, 0],
        curve: "smooth",
      },
      plotOptions: {
        bar: {
          columnWidth: "60%",
          height: 1900,
          dataLabels: {
            total: {
              enabled: true, // Enable total labels to show sum of both salary columns
              offsetY: -20,
              style: {
                fontSize: "14px",
                fontWeight: "500",
                color: "#333",
              },
              formatter: function (val) {
                if (val === null || val === undefined || isNaN(val)) return "$0";
                return "$" + formatWagesRevenueValue(val);
              },
            },
          },
        },
      },
      fill: {
        opacity: [1, 1, 1],
      },
      labels: wagesCategories,
      markers: {
        size: [5, 0, 0],
        fontSize: "14px",
        strokeColors: "#fff",
        strokeWidth: 2,
        fillOpacity: 1,
        hover: {
          size: 7,
        },
      },
      xaxis: {
        labels: {
          style: {
            colors: "#666",
            fontSize: "14px",
          },
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
      },
      yaxis: {
        show: false,
      },
      colors: colors.wagesRevenue,
      legend: {
        position: "bottom",
        horizontalAlign: "center",
        fontSize: "14px",
        fontWeight: "400",
        markers: {
          width: 8,
          height: 8,
          radius: 4,
        },
        labels: {
          colors: "#333",
          useSeriesColors: false,
        },
        itemMargin: {
          horizontal: 15,
          vertical: 4,
        },
        offsetY: 10,
        onItemClick: {
          toggleDataSeries: false,
        },
        onItemHover: {
          highlightDataSeries: false,
        },
      },
      tooltip: {
        shared: true,
        intersect: false,
        y: [
          {
            formatter: function (val) {
              if (val === null || val === undefined || isNaN(val)) return "$0";
              return "$" + formatWagesRevenueValue(val);
            },
          },
          {
            formatter: function (val) {
              if (val === null || val === undefined || isNaN(val)) return "$0";
              return "$" + formatWagesRevenueValue(val);
            },
          },
          {
            formatter: function (val) {
              if (val === null || val === undefined || isNaN(val)) return "$0";
              return "$" + formatWagesRevenueValue(val);
            },
          },
        ],
      },
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 20,
          bottom: 0,
        },
      },
    };


    // Clear existing charts before rendering new ones
    const clearAndRenderChart = (ref, options, chartName, chartInstanceRef = null) => {
      if (ref.current) {
        // Clear any existing chart
        if (chartInstanceRef && chartInstanceRef.current) {
          chartInstanceRef.current.destroy();
          chartInstanceRef.current = null;
        }
        ref.current.innerHTML = "";

        // Wait a tick before rendering to ensure DOM is cleared
        setTimeout(() => {
          if (ref.current) {
            try {
              const chart = new ApexCharts(ref.current, options);
              chart.render();

              // Store chart instance for export functionality
              if (chartInstanceRef) {
                chartInstanceRef.current = chart;
              }

              // Make all charts globally accessible for export
              if (chartName === "ROA/ROE") {
                window.roaRoeChart = chart;
              } else if (chartName === "Expenses Pie") {
                window.expensesPieChart = chart;
              } else if (chartName === "Monthly Expenses") {
                window.expensesMonthlyChart = chart;
              } else if (chartName === "Wages vs Revenue") {
                window.wagesRevenueChart = chart;
              }
            } catch (error) {
              console.error(
                `ExpenseSummary - Error rendering ${chartName} chart:`,
                error
              );
            }
          }
        }, 10);
      }
    };

    // Get enabled charts and assign chart options
    const enabledCharts = getEnabledCharts();

    // Assign chart options to enabled charts
    enabledCharts.forEach(chart => {
      switch (chart.key) {
        case 'roaAndRoe':
          chart.options = roaRoeOptions;
          chart.name = 'ROA/ROE';
          chart.chartInstanceRef = roaRoeChartRef;
          break;
        case 'expensesTopAccounts':
          chart.options = expensesPieOptions;
          chart.name = 'Expenses Pie';
          chart.chartInstanceRef = expensesPieChartRef;
          break;
        case 'expensesTopAccountsMonthly':
          chart.options = expensesMonthlyOptions;
          chart.name = 'Monthly Expenses';
          chart.chartInstanceRef = expensesMonthlyChartRef;
          break;
        case 'expensesWagesVsRevenueMonthly':
          chart.options = wagesRevenueOptions;
          chart.name = 'Wages vs Revenue';
          chart.chartInstanceRef = wagesRevenueChartRef;
          break;
      }
    });

    // Helper function to check if data array has meaningful values (not all zeros)
    const hasMeaningfulData = (dataArray) => {
      return dataArray && dataArray.length > 0 && dataArray.some(val => parseFloat(val) !== 0);
    };

    // Render charts with fallback for empty/zero data
    enabledCharts.forEach(({ ref, options, name, chartInstanceRef, key }) => {
      if (ref.current) {
        let hasData = false;

        // Check if chart has meaningful data based on chart type
        if (key === 'roaAndRoe') {
          const { roaData, roeData } = processRoeRoaData();
          hasData = hasMeaningfulData(roaData) || hasMeaningfulData(roeData);
        } else if (key === 'expensesTopAccounts') {
          const { data: pieData } = processExpensesPieData();
          hasData = pieData && pieData.length > 0 && pieData.some(val => parseFloat(val) !== 0);
        } else if (key === 'expensesTopAccountsMonthly') {
          const { series: monthlyExpensesSeries } = processMonthlyExpensesData();
          hasData = monthlyExpensesSeries && monthlyExpensesSeries.length > 0 &&
                   monthlyExpensesSeries.some(series => series.data && series.data.some(val => parseFloat(val) !== 0));
        } else if (key === 'expensesWagesVsRevenueMonthly') {
          const { income, salariesGA, salariesSales } = processWagesRevenueData();
          hasData = hasMeaningfulData(income) || hasMeaningfulData(salariesGA) || hasMeaningfulData(salariesSales);
        }

        if (hasData) {
          clearAndRenderChart(ref, options, name, chartInstanceRef);
        } 
        else {
          ref.current.innerHTML = `<div class="flex items-center justify-center h-64 text-gray-500">No meaningful ${name.toLowerCase()} data available</div>`;
        }
      }
    });

    // Clear all chart containers that are not being used
    [roaRoeRef, expensesPieRef, expensesMonthlyRef, wagesRevenueRef].forEach((ref) => {
      if (ref.current && !enabledCharts.some(chart => chart.ref === ref)) {
        ref.current.innerHTML = "";
      }
    });
  };


  // Add a fallback if reportData exists but has no usable data
  const hasAnyUsableData = () => {
    const { roaData, roeData } = processRoeRoaData();
    const { data: pieData } = processExpensesPieData();
    const { series: monthlyExpensesSeries } = processMonthlyExpensesData();
    const { income } = processWagesRevenueData();

    return (
      roaData.length > 0 ||
      roeData.length > 0 ||
      pieData.length > 0 ||
      monthlyExpensesSeries.length > 0 ||
      income.length > 0
    );
  };

  const formatHeaderPeriod = (startYear, startMonth) => {
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    if (!startYear || !startMonth) {
      return " "; // fallback
    }

    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index

    return `${startMonthName} ${startYear}`;
  };

  const formatHeaderStyle = () => {
    const style = { ...headerTextStyle };
    if (style.fontSize) {
      const fontSize = parseInt(style.fontSize);
      style.fontSize = `${fontSize / 2}px`;
    }
    return style;
  };

  const formatCompanyName = (companyName) => {
    if (!companyName) return '';

    if (companyName.length > 15) {
      return companyName.substring(0, 15) + '...';
    }

    return companyName;
  };

  // Function to determine which charts should be rendered and their order
  const getEnabledCharts = () => {
    const allCharts = [
      {
        key: 'roaAndRoe',
        title: 'Return on Assets and Equity',
        ref: roaRoeRef,
        options: null, // Will be set in initializeCharts
        hasData: () => {
          const { roaData, roeData } = processRoeRoaData();
          return roaData.length > 0 || roeData.length > 0;
        },
        hasDescription: true,
        description: {
          sections: [
            {
              title: 'Return on Assets',
              content: `Indicates how well ${reportData?.companyName} is using capital invested in Assets to generate Total Income. The higher the return, the more productive and efficient management is in utilizing economic resources the business has.`
            },
            {
              title: 'Return on Equity',
              content: 'Indicates how efficient company management is at generating growth from its Equity financing. Because Equity is equal to a company\'s Assets minus Liabilities, ROE is also considered the Return on Net Assets.'
            }
          ]
        }
      },
      {
        key: 'expensesTopAccounts',
        title: 'Expenses: Top Accounts',
        ref: expensesPieRef,
        options: null,
        hasData: () => {
          const { data: pieData } = processExpensesPieData();
          return pieData.length > 0;
        },
        titleMarginBottom: 'mb-20'
      },
      {
        key: 'expensesTopAccountsMonthly',
        title: 'Expenses: Top Accounts Monthly',
        ref: expensesMonthlyRef,
        options: null,
        hasData: () => {
          const { series: monthlyExpensesSeries } = processMonthlyExpensesData();
          return monthlyExpensesSeries.length > 0;
        },
        containerClass: 'expenses-monthly-apex'
      },
      {
        key: 'expensesWagesVsRevenueMonthly',
        title: 'Expenses: Wages Vs Revenue Monthly',
        ref: wagesRevenueRef,
        options: null,
        hasData: () => {
          const { income } = processWagesRevenueData();
          return income.length > 0;
        },
        containerMarginBottom: 'mb-2'
      }
    ];

    // Filter charts based on settings and data availability
    return allCharts.filter(chart =>
      shouldDisplayChart(chart.key)
    );
  };

  // Get enabled charts for dynamic layout
  const enabledCharts = getEnabledCharts();

  // Split charts between upper and lower divs
  // Upper div can hold up to 2 charts, lower div gets the rest
  const upperDivCharts = enabledCharts.slice(0, 2);
  const lowerDivCharts = enabledCharts.slice(2);

  // Helper function to render a chart component
  const renderChart = (chart) => (
    <div key={chart.key} className={`bg-white p-6 border-b-4 border-blue-900 ${chart.containerClass || ''}`}>
      <div
        className={`text-2xl font-semibold text-teal-600 mb-5 ${chart.titleMarginBottom || ''}`}
        style={subHeadingTextStyle}
      >
        {chart.title}
      </div>
      <div ref={chart.ref} className={chart.containerMarginBottom || ''}></div>
      {chart.hasDescription && (
        <div className="mt-6">
          {chart.description.sections.map((section, index) => (
            <div key={index} className={index === chart.description.sections.length - 1 ? "mb-8" : "mb-4"}>
              <div
                className="text-teal-600 text-xl font-semibold"
                style={{ ...subHeadingTextStyle, fontWeight: "lighter" }}
              >
                {section.title}
              </div>
              <p
                className="text-gray-700 leading-relaxed"
                style={contentTextStyle}
              >
                {section.content}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  // Don't render anything if no charts are enabled
  if (enabledCharts.length === 0) {
    return null;
  }

  return (
    <div className="p-5">
      <div className="max-w-6xl h-[410mm] mx-auto bg-white flex flex-col gap-10 p-10 mb-4">
        {/* Header Section */}
        <div className="component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2">
          <h1
            className="text-4xl font-bold text-gray-800 m-0"
            style={headerTextStyle}
          >
            Expense Summary
          </h1>
          <p className="text-lg text-gray-600 m-0" style={formatHeaderStyle()}>
            {formatHeaderPeriod(reportData?.FYEndYear, reportData?.FYStartMonth)} | {formatCompanyName(reportData?.companyName)}
          </p>
        </div>

        {/* Dynamically render charts for upper div */}
        {upperDivCharts.map(chart => renderChart(chart))}

      </div>

      {/* Only render lower div if there are charts to display */}
      {lowerDivCharts.length > 0 && (
        <div className="max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-16 p-10">
          <div className="component-header flex items-center justify-between gap-4 mb-8 pt-2 border-b-4 border-blue-900 pb-2">
            <h1
              className="text-4xl font-bold text-gray-800 m-0"
              style={headerTextStyle}
            >
              Expense Summary
            </h1>
            <p className="text-lg text-gray-600 m-0" style={formatHeaderStyle()}>
              {formatHeaderPeriod(reportData?.FYEndYear, reportData?.FYStartMonth)} | {formatCompanyName(reportData?.companyName)}
            </p>
          </div>

          {/* Dynamically render charts for lower div */}
          {lowerDivCharts.map(chart => renderChart(chart))}
        </div>
      )}
    </div>
  );
};

export default ExpenseSummaryDashboard;