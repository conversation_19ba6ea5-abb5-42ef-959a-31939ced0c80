[{"C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\layout\\LayOut.jsx": "4", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\EditReportPage.js": "5", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Auth.jsx": "6", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ResetLink.jsx": "7", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ForgotPassword.jsx": "8", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\CheckEmail.jsx": "9", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\Login.jsx": "10", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ResetPassword.jsx": "11", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\faqs\\Faqs.jsx": "12", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Dashboard\\Dashboard.jsx": "13", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\examples\\Examples.jsx": "14", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\View.jsx": "15", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompanies.jsx": "16", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\faq.js": "17", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\report.js": "18", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\auth.js": "19", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\example.js": "20", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\EditReport.jsx": "21", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\TopBar.jsx": "22", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\company.js": "23", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\settings.js": "24", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Companies.jsx": "25", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Create.jsx": "26", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Reports.jsx": "27", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\BreadCrumbs.jsx": "28", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\shared.js": "29", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompaniesList.jsx": "30", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\CSVModal.jsx": "31", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\hooks\\useDebounceSearch.jsx": "32", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Edit.jsx": "33", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompaniesModal.jsx": "34", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\axiosInstance.js": "35", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\cookies.js": "36", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\NoDataFound.jsx": "37", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\markets.js": "38", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ReportModal.jsx": "39", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ShareUsersModal.jsx": "40", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\Table.jsx": "41", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\enums\\report.enum.js": "42", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\CustomizeReport.jsx": "43", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\QboCallback\\qboCallback.jsx": "44", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\pdf.js": "45", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\ReportSummary.jsx": "46", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\ExpenseSummary.jsx": "47", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\FiscalYear.jsx": "48", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\OperationalEfficiency.jsx": "49", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\LiquiditySummary.jsx": "50", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\YearToDate.jsx": "51", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\Monthly.jsx": "52", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\TableOfContents.jsx": "53", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\MonthTrailing.jsx": "54", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\BalanceSheet.jsx": "55", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\CoverPage.jsx": "56", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\pageNumbering.js": "57", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\qbo.js": "58", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\UploadableSection.jsx": "59", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\QuickBooksConnection.jsx": "60", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ReportSettings.jsx": "61", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\contentSettings.js": "62", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\customizeReportService.js": "63", "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\QuickBooksConnect\\QuickBooksConnect.jsx": "64"}, {"size": 507, "mtime": 1756456910065, "results": "65", "hashOfConfig": "66"}, {"size": 2753, "mtime": 1756905380525, "results": "67", "hashOfConfig": "66"}, {"size": 375, "mtime": 1756456910096, "results": "68", "hashOfConfig": "66"}, {"size": 305, "mtime": 1756456910067, "results": "69", "hashOfConfig": "66"}, {"size": 886, "mtime": 1756464224277, "results": "70", "hashOfConfig": "66"}, {"size": 182, "mtime": 1756456910069, "results": "71", "hashOfConfig": "66"}, {"size": 2138, "mtime": 1756464224236, "results": "72", "hashOfConfig": "66"}, {"size": 4322, "mtime": 1756456910072, "results": "73", "hashOfConfig": "66"}, {"size": 2441, "mtime": 1756456910071, "results": "74", "hashOfConfig": "66"}, {"size": 5717, "mtime": 1756888106486, "results": "75", "hashOfConfig": "66"}, {"size": 6189, "mtime": 1756464210205, "results": "76", "hashOfConfig": "66"}, {"size": 2963, "mtime": 1756456910095, "results": "77", "hashOfConfig": "66"}, {"size": 2268, "mtime": 1756456910093, "results": "78", "hashOfConfig": "66"}, {"size": 13394, "mtime": 1756464224274, "results": "79", "hashOfConfig": "66"}, {"size": 45608, "mtime": 1756902270380, "results": "80", "hashOfConfig": "66"}, {"size": 2343, "mtime": 1756464224249, "results": "81", "hashOfConfig": "66"}, {"size": 139, "mtime": 1756456910115, "results": "82", "hashOfConfig": "66"}, {"size": 801, "mtime": 1756456910118, "results": "83", "hashOfConfig": "66"}, {"size": 549, "mtime": 1756889575284, "results": "84", "hashOfConfig": "66"}, {"size": 311, "mtime": 1756456910114, "results": "85", "hashOfConfig": "66"}, {"size": 34297, "mtime": 1756464224241, "results": "86", "hashOfConfig": "66"}, {"size": 3608, "mtime": 1756464224294, "results": "87", "hashOfConfig": "66"}, {"size": 1491, "mtime": 1756456910112, "results": "88", "hashOfConfig": "66"}, {"size": 557, "mtime": 1756463597466, "results": "89", "hashOfConfig": "66"}, {"size": 13482, "mtime": 1756464224237, "results": "90", "hashOfConfig": "66"}, {"size": 26570, "mtime": 1756464224239, "results": "91", "hashOfConfig": "66"}, {"size": 12498, "mtime": 1756961490769, "results": "92", "hashOfConfig": "66"}, {"size": 1201, "mtime": 1756464224292, "results": "93", "hashOfConfig": "66"}, {"size": 688, "mtime": 1756464224299, "results": "94", "hashOfConfig": "66"}, {"size": 4023, "mtime": 1756456910091, "results": "95", "hashOfConfig": "66"}, {"size": 9364, "mtime": 1756456910082, "results": "96", "hashOfConfig": "66"}, {"size": 398, "mtime": 1756456910064, "results": "97", "hashOfConfig": "66"}, {"size": 25971, "mtime": 1756464224240, "results": "98", "hashOfConfig": "66"}, {"size": 5490, "mtime": 1756456910092, "results": "99", "hashOfConfig": "66"}, {"size": 1370, "mtime": 1756464224289, "results": "100", "hashOfConfig": "66"}, {"size": 756, "mtime": 1756464224297, "results": "101", "hashOfConfig": "66"}, {"size": 1596, "mtime": 1756456910125, "results": "102", "hashOfConfig": "66"}, {"size": 703, "mtime": 1756456910117, "results": "103", "hashOfConfig": "66"}, {"size": 26329, "mtime": 1759734181252, "results": "104", "hashOfConfig": "66"}, {"size": 6399, "mtime": 1756456910088, "results": "105", "hashOfConfig": "66"}, {"size": 4226, "mtime": 1756464224293, "results": "106", "hashOfConfig": "66"}, {"size": 191, "mtime": 1756464224235, "results": "107", "hashOfConfig": "66"}, {"size": 109106, "mtime": 1759985945380, "results": "108", "hashOfConfig": "66"}, {"size": 16373, "mtime": 1756905380528, "results": "109", "hashOfConfig": "66"}, {"size": 1331, "mtime": 1756464224290, "results": "110", "hashOfConfig": "66"}, {"size": 15715, "mtime": 1759985945391, "results": "111", "hashOfConfig": "66"}, {"size": 36984, "mtime": 1759985945384, "results": "112", "hashOfConfig": "66"}, {"size": 35455, "mtime": 1759999420873, "results": "113", "hashOfConfig": "66"}, {"size": 29237, "mtime": 1759985945390, "results": "114", "hashOfConfig": "66"}, {"size": 15775, "mtime": 1759994695138, "results": "115", "hashOfConfig": "66"}, {"size": 17758, "mtime": 1759141227832, "results": "116", "hashOfConfig": "66"}, {"size": 18870, "mtime": 1759140844161, "results": "117", "hashOfConfig": "66"}, {"size": 7752, "mtime": 1759985945392, "results": "118", "hashOfConfig": "66"}, {"size": 10200, "mtime": 1759141237895, "results": "119", "hashOfConfig": "66"}, {"size": 21601, "mtime": 1759141556681, "results": "120", "hashOfConfig": "66"}, {"size": 3300, "mtime": 1759985945382, "results": "121", "hashOfConfig": "66"}, {"size": 4264, "mtime": 1756978912887, "results": "122", "hashOfConfig": "66"}, {"size": 1198, "mtime": 1756464224291, "results": "123", "hashOfConfig": "66"}, {"size": 11921, "mtime": 1756467113937, "results": "124", "hashOfConfig": "66"}, {"size": 10932, "mtime": 1756797941807, "results": "125", "hashOfConfig": "66"}, {"size": 25970, "mtime": 1759230649736, "results": "126", "hashOfConfig": "66"}, {"size": 1226, "mtime": 1756464224289, "results": "127", "hashOfConfig": "66"}, {"size": 1280, "mtime": 1756886099441, "results": "128", "hashOfConfig": "66"}, {"size": 16486, "mtime": 1756902270386, "results": "129", "hashOfConfig": "66"}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cg5eed", {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\layout\\LayOut.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\EditReportPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Auth.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ResetLink.jsx", ["322"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ForgotPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\CheckEmail.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\Login.jsx", ["323"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Auth\\Components\\ResetPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\faqs\\Faqs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Dashboard\\Dashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\examples\\Examples.jsx", ["324"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\View.jsx", ["325", "326", "327", "328"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompanies.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\faq.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\report.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\example.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\EditReport.jsx", ["329", "330", "331", "332", "333", "334", "335", "336", "337", "338"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\TopBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\company.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\settings.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Companies.jsx", ["339", "340"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Create.jsx", ["341", "342"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Reports.jsx", ["343", "344"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\BreadCrumbs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\shared.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompaniesList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\CSVModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\hooks\\useDebounceSearch.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\Edit.jsx", ["345", "346", "347", "348"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\shared-companies\\SharedCompaniesModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\axiosInstance.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\cookies.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\NoDataFound.jsx", ["349"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\markets.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ReportModal.jsx", ["350", "351", "352", "353", "354", "355", "356", "357", "358", "359", "360"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ShareUsersModal.jsx", ["361", "362"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\shared-components\\Table.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\enums\\report.enum.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\CustomizeReport.jsx", ["363", "364", "365", "366", "367", "368", "369", "370"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\QboCallback\\qboCallback.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\pdf.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\ReportSummary.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\ExpenseSummary.jsx", ["371", "372", "373", "374"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\FiscalYear.jsx", ["375", "376", "377", "378", "379", "380", "381"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\OperationalEfficiency.jsx", ["382", "383"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\LiquiditySummary.jsx", ["384", "385"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\YearToDate.jsx", ["386", "387"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\Monthly.jsx", ["388", "389"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\TableOfContents.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\MonthTrailing.jsx", ["390"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\BalanceSheet.jsx", ["391", "392"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\reports\\ReportPages\\CoverPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\utils\\pageNumbering.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\qbo.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\UploadableSection.jsx", ["393", "394", "395", "396", "397", "398", "399"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\QuickBooksConnection.jsx", ["400", "401", "402", "403", "404"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\Companies\\Components\\ReportSettings.jsx", ["405", "406"], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\contentSettings.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\services\\customizeReportService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Valisights\\valisight-app\\src\\pages\\QuickBooksConnect\\QuickBooksConnect.jsx", ["407", "408", "409", "410", "411", "412", "413", "414", "415", "416"], [], {"ruleId": "417", "severity": 1, "message": "418", "line": 2, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 2, "endColumn": 21}, {"ruleId": "417", "severity": 1, "message": "421", "line": 23, "column": 32, "nodeType": "419", "messageId": "420", "endLine": 23, "endColumn": 42}, {"ruleId": "422", "severity": 1, "message": "423", "line": 363, "column": 21, "nodeType": "424", "endLine": 363, "endColumn": 66}, {"ruleId": "417", "severity": 1, "message": "425", "line": 16, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 16, "endColumn": 14}, {"ruleId": "417", "severity": 1, "message": "426", "line": 31, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 31, "endColumn": 19}, {"ruleId": "417", "severity": 1, "message": "427", "line": 31, "column": 21, "nodeType": "419", "messageId": "420", "endLine": 31, "endColumn": 30}, {"ruleId": "417", "severity": 1, "message": "428", "line": 59, "column": 24, "nodeType": "419", "messageId": "420", "endLine": 59, "endColumn": 39}, {"ruleId": "429", "severity": 1, "message": "430", "line": 222, "column": 37, "nodeType": "431", "messageId": "432", "endLine": 232, "endColumn": 12}, {"ruleId": "429", "severity": 1, "message": "433", "line": 235, "column": 37, "nodeType": "431", "messageId": "432", "endLine": 376, "endColumn": 12}, {"ruleId": "417", "severity": 1, "message": "434", "line": 237, "column": 28, "nodeType": "419", "messageId": "420", "endLine": 237, "endColumn": 33}, {"ruleId": "417", "severity": 1, "message": "435", "line": 237, "column": 42, "nodeType": "419", "messageId": "420", "endLine": 237, "endColumn": 48}, {"ruleId": "436", "severity": 1, "message": "437", "line": 282, "column": 30, "nodeType": "438", "messageId": "439", "endLine": 282, "endColumn": 32}, {"ruleId": "436", "severity": 1, "message": "437", "line": 283, "column": 30, "nodeType": "438", "messageId": "439", "endLine": 283, "endColumn": 32}, {"ruleId": "436", "severity": 1, "message": "437", "line": 284, "column": 30, "nodeType": "438", "messageId": "439", "endLine": 284, "endColumn": 32}, {"ruleId": "436", "severity": 1, "message": "437", "line": 285, "column": 30, "nodeType": "438", "messageId": "439", "endLine": 285, "endColumn": 32}, {"ruleId": "440", "severity": 1, "message": "441", "line": 578, "column": 6, "nodeType": "442", "endLine": 578, "endColumn": 28, "suggestions": "443"}, {"ruleId": "417", "severity": 1, "message": "444", "line": 729, "column": 13, "nodeType": "419", "messageId": "420", "endLine": 729, "endColumn": 21}, {"ruleId": "417", "severity": 1, "message": "445", "line": 33, "column": 8, "nodeType": "419", "messageId": "420", "endLine": 33, "endColumn": 21}, {"ruleId": "440", "severity": 1, "message": "446", "line": 138, "column": 6, "nodeType": "442", "endLine": 138, "endColumn": 58, "suggestions": "447"}, {"ruleId": "417", "severity": 1, "message": "448", "line": 29, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 29, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "449", "line": 58, "column": 26, "nodeType": "419", "messageId": "420", "endLine": 58, "endColumn": 43}, {"ruleId": "417", "severity": 1, "message": "450", "line": 47, "column": 9, "nodeType": "419", "messageId": "420", "endLine": 47, "endColumn": 26}, {"ruleId": "440", "severity": 1, "message": "451", "line": 132, "column": 6, "nodeType": "442", "endLine": 132, "endColumn": 38, "suggestions": "452"}, {"ruleId": "417", "severity": 1, "message": "449", "line": 48, "column": 26, "nodeType": "419", "messageId": "420", "endLine": 48, "endColumn": 43}, {"ruleId": "417", "severity": 1, "message": "453", "line": 64, "column": 9, "nodeType": "419", "messageId": "420", "endLine": 64, "endColumn": 25}, {"ruleId": "454", "severity": 1, "message": "455", "line": 614, "column": 21, "nodeType": "456", "messageId": "439", "endLine": 614, "endColumn": 36}, {"ruleId": "454", "severity": 1, "message": "457", "line": 615, "column": 21, "nodeType": "456", "messageId": "439", "endLine": 615, "endColumn": 32}, {"ruleId": "417", "severity": 1, "message": "458", "line": 7, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 7, "endColumn": 20}, {"ruleId": "417", "severity": 1, "message": "459", "line": 6, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 6, "endColumn": 18}, {"ruleId": "417", "severity": 1, "message": "460", "line": 12, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 12, "endColumn": 9}, {"ruleId": "417", "severity": 1, "message": "461", "line": 13, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 13, "endColumn": 11}, {"ruleId": "417", "severity": 1, "message": "462", "line": 14, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 14, "endColumn": 14}, {"ruleId": "417", "severity": 1, "message": "463", "line": 15, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 15, "endColumn": 13}, {"ruleId": "417", "severity": 1, "message": "464", "line": 110, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 110, "endColumn": 23}, {"ruleId": "417", "severity": 1, "message": "465", "line": 110, "column": 25, "nodeType": "419", "messageId": "420", "endLine": 110, "endColumn": 41}, {"ruleId": "417", "severity": 1, "message": "466", "line": 161, "column": 9, "nodeType": "419", "messageId": "420", "endLine": 161, "endColumn": 26}, {"ruleId": "417", "severity": 1, "message": "467", "line": 164, "column": 9, "nodeType": "419", "messageId": "420", "endLine": 164, "endColumn": 28}, {"ruleId": "436", "severity": 1, "message": "437", "line": 195, "column": 77, "nodeType": "438", "messageId": "439", "endLine": 195, "endColumn": 79}, {"ruleId": "436", "severity": 1, "message": "437", "line": 199, "column": 73, "nodeType": "438", "messageId": "439", "endLine": 199, "endColumn": 75}, {"ruleId": "417", "severity": 1, "message": "468", "line": 63, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 63, "endColumn": 24}, {"ruleId": "440", "severity": 1, "message": "469", "line": 77, "column": 8, "nodeType": "442", "endLine": 77, "endColumn": 45, "suggestions": "470"}, {"ruleId": "417", "severity": 1, "message": "471", "line": 47, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 47, "endColumn": 25}, {"ruleId": "417", "severity": 1, "message": "472", "line": 63, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 63, "endColumn": 26}, {"ruleId": "417", "severity": 1, "message": "473", "line": 63, "column": 28, "nodeType": "419", "messageId": "420", "endLine": 63, "endColumn": 47}, {"ruleId": "417", "severity": 1, "message": "474", "line": 441, "column": 13, "nodeType": "419", "messageId": "420", "endLine": 441, "endColumn": 19}, {"ruleId": "417", "severity": 1, "message": "475", "line": 454, "column": 17, "nodeType": "419", "messageId": "420", "endLine": 454, "endColumn": 30}, {"ruleId": "417", "severity": 1, "message": "476", "line": 724, "column": 15, "nodeType": "419", "messageId": "420", "endLine": 724, "endColumn": 25}, {"ruleId": "440", "severity": 1, "message": "477", "line": 1129, "column": 6, "nodeType": "442", "endLine": 1129, "endColumn": 27, "suggestions": "478"}, {"ruleId": "440", "severity": 1, "message": "479", "line": 1145, "column": 6, "nodeType": "442", "endLine": 1145, "endColumn": 60, "suggestions": "480"}, {"ruleId": "440", "severity": 1, "message": "481", "line": 74, "column": 6, "nodeType": "442", "endLine": 74, "endColumn": 35, "suggestions": "482"}, {"ruleId": "454", "severity": 1, "message": "483", "line": 489, "column": 7, "nodeType": "456", "messageId": "439", "endLine": 489, "endColumn": 17}, {"ruleId": "484", "severity": 1, "message": "485", "line": 913, "column": 7, "nodeType": "486", "messageId": "487", "endLine": 934, "endColumn": 8}, {"ruleId": "417", "severity": 1, "message": "488", "line": 982, "column": 9, "nodeType": "419", "messageId": "420", "endLine": 982, "endColumn": 25}, {"ruleId": "417", "severity": 1, "message": "489", "line": 27, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 27, "endColumn": 31}, {"ruleId": "417", "severity": 1, "message": "490", "line": 31, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 31, "endColumn": 29}, {"ruleId": "417", "severity": 1, "message": "491", "line": 35, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 35, "endColumn": 27}, {"ruleId": "440", "severity": 1, "message": "481", "line": 78, "column": 6, "nodeType": "442", "endLine": 78, "endColumn": 35, "suggestions": "492"}, {"ruleId": "436", "severity": 1, "message": "437", "line": 353, "column": 86, "nodeType": "438", "messageId": "439", "endLine": 353, "endColumn": 88}, {"ruleId": "436", "severity": 1, "message": "437", "line": 355, "column": 79, "nodeType": "438", "messageId": "439", "endLine": 355, "endColumn": 81}, {"ruleId": "484", "severity": 1, "message": "485", "line": 711, "column": 7, "nodeType": "486", "messageId": "487", "endLine": 728, "endColumn": 8}, {"ruleId": "440", "severity": 1, "message": "481", "line": 67, "column": 6, "nodeType": "442", "endLine": 67, "endColumn": 40, "suggestions": "493"}, {"ruleId": "484", "severity": 1, "message": "485", "line": 614, "column": 7, "nodeType": "486", "messageId": "487", "endLine": 635, "endColumn": 8}, {"ruleId": "417", "severity": 1, "message": "494", "line": 28, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 28, "endColumn": 23}, {"ruleId": "440", "severity": 1, "message": "481", "line": 45, "column": 6, "nodeType": "442", "endLine": 45, "endColumn": 38, "suggestions": "495"}, {"ruleId": "417", "severity": 1, "message": "496", "line": 110, "column": 15, "nodeType": "419", "messageId": "420", "endLine": 110, "endColumn": 30}, {"ruleId": "440", "severity": 1, "message": "497", "line": 150, "column": 6, "nodeType": "442", "endLine": 150, "endColumn": 35, "suggestions": "498"}, {"ruleId": "417", "severity": 1, "message": "496", "line": 128, "column": 15, "nodeType": "419", "messageId": "420", "endLine": 128, "endColumn": 30}, {"ruleId": "440", "severity": 1, "message": "497", "line": 180, "column": 6, "nodeType": "442", "endLine": 180, "endColumn": 35, "suggestions": "499"}, {"ruleId": "440", "severity": 1, "message": "497", "line": 103, "column": 6, "nodeType": "442", "endLine": 103, "endColumn": 35, "suggestions": "500"}, {"ruleId": "417", "severity": 1, "message": "501", "line": 127, "column": 13, "nodeType": "419", "messageId": "420", "endLine": 127, "endColumn": 32}, {"ruleId": "440", "severity": 1, "message": "497", "line": 227, "column": 6, "nodeType": "442", "endLine": 227, "endColumn": 35, "suggestions": "502"}, {"ruleId": "417", "severity": 1, "message": "503", "line": 11, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 11, "endColumn": 19}, {"ruleId": "417", "severity": 1, "message": "504", "line": 13, "column": 8, "nodeType": "419", "messageId": "420", "endLine": 13, "endColumn": 16}, {"ruleId": "417", "severity": 1, "message": "505", "line": 14, "column": 8, "nodeType": "419", "messageId": "420", "endLine": 14, "endColumn": 18}, {"ruleId": "417", "severity": 1, "message": "506", "line": 15, "column": 8, "nodeType": "419", "messageId": "420", "endLine": 15, "endColumn": 23}, {"ruleId": "417", "severity": 1, "message": "507", "line": 80, "column": 13, "nodeType": "419", "messageId": "420", "endLine": 80, "endColumn": 20}, {"ruleId": "417", "severity": 1, "message": "508", "line": 81, "column": 13, "nodeType": "419", "messageId": "420", "endLine": 81, "endColumn": 17}, {"ruleId": "417", "severity": 1, "message": "509", "line": 82, "column": 13, "nodeType": "419", "messageId": "420", "endLine": 82, "endColumn": 25}, {"ruleId": "417", "severity": 1, "message": "510", "line": 10, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 10, "endColumn": 7}, {"ruleId": "417", "severity": 1, "message": "511", "line": 18, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 18, "endColumn": 10}, {"ruleId": "417", "severity": 1, "message": "512", "line": 24, "column": 11, "nodeType": "419", "messageId": "420", "endLine": 24, "endColumn": 19}, {"ruleId": "417", "severity": 1, "message": "513", "line": 25, "column": 13, "nodeType": "419", "messageId": "420", "endLine": 25, "endColumn": 23}, {"ruleId": "417", "severity": 1, "message": "514", "line": 46, "column": 24, "nodeType": "419", "messageId": "420", "endLine": 46, "endColumn": 39}, {"ruleId": "417", "severity": 1, "message": "515", "line": 35, "column": 9, "nodeType": "419", "messageId": "420", "endLine": 35, "endColumn": 14}, {"ruleId": "417", "severity": 1, "message": "515", "line": 219, "column": 9, "nodeType": "419", "messageId": "420", "endLine": 219, "endColumn": 14}, {"ruleId": "417", "severity": 1, "message": "516", "line": 9, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 9, "endColumn": 7}, {"ruleId": "417", "severity": 1, "message": "517", "line": 12, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 12, "endColumn": 8}, {"ruleId": "417", "severity": 1, "message": "518", "line": 13, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 13, "endColumn": 10}, {"ruleId": "417", "severity": 1, "message": "519", "line": 18, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 18, "endColumn": 7}, {"ruleId": "417", "severity": 1, "message": "520", "line": 19, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 19, "endColumn": 11}, {"ruleId": "417", "severity": 1, "message": "521", "line": 20, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 20, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "522", "line": 21, "column": 3, "nodeType": "419", "messageId": "420", "endLine": 21, "endColumn": 15}, {"ruleId": "417", "severity": 1, "message": "523", "line": 31, "column": 21, "nodeType": "419", "messageId": "420", "endLine": 31, "endColumn": 39}, {"ruleId": "417", "severity": 1, "message": "524", "line": 34, "column": 17, "nodeType": "419", "messageId": "420", "endLine": 34, "endColumn": 31}, {"ruleId": "417", "severity": 1, "message": "525", "line": 55, "column": 10, "nodeType": "419", "messageId": "420", "endLine": 55, "endColumn": 19}, "no-unused-vars", "'useNavigate' is defined but never used.", "Identifier", "unusedVar", "'rememberMe' is assigned a value but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "'Link' is defined but never used.", "'getCookie' is defined but never used.", "'setCookie' is defined but never used.", "'setSelectedFile' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'sectionHeaderX'.", "ArrowFunctionExpression", "unsafeRefs", "Function declared in a loop contains unsafe references to variable(s) 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'sectionHeaderX', 'isReportSummaryHeading', 'hasInsertedLineBreak', 'styledHTML', 'hasInsertedLineBreak', 'styledHTML', 'currentSection', 'currentSection', 'isReportSummaryHeading', 'isReportSummaryHeading', 'styledHTML'.", "'skewX' is assigned a value but never used.", "'scaleY' is assigned a value but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "react-hooks/exhaustive-deps", "React Hook React.useEffect has a missing dependency: 'report?.request_type'. Either include it or remove the dependency array.", "ArrayExpression", ["526"], "'response' is assigned a value but never used.", "'FilterAltIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchCompanies'. Either include it or remove the dependency array.", ["527"], "'revokeAccess' is defined but never used.", "'setIsCreatedModal' is assigned a value but never used.", "'requestTypeLabels' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'companyReports'. Either include it or remove the dependency array.", ["528"], "'handleRemoveUser' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'backgroundColor'.", "ObjectExpression", "Duplicate key 'borderColor'.", "'dialogOpen' is assigned a value but never used.", "'GrRevert' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'deepSightMode' is assigned a value but never used.", "'setDeepSightMode' is assigned a value but never used.", "'fiscalYearOptions' is assigned a value but never used.", "'getFiscalYearPeriod' is assigned a value but never used.", "'handleAddUser' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array. If 'fetchUsers' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["529"], "'initialSettings' is assigned a value but never used.", "'selectedTemplate' is assigned a value but never used.", "'setSelectedTemplate' is assigned a value but never used.", "'failed' is assigned a value but never used.", "'uploadResults' is assigned a value but never used.", "'metricGrid' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchContentSettings', 'initializeDataWithConnectionCheck', and 'initializeSettings'. Either include them or remove the dependency array.", ["530"], "React Hook useEffect has missing dependencies: 'clearRedirectTimer' and 'startRedirectTimer'. Either include them or remove the dependency array.", ["531"], "React Hook useEffect has missing dependencies: 'initializeCharts' and 'isDataLoaded'. Either include them or remove the dependency array.", ["532"], "Duplicate key 'dataLabels'.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'hasAnyUsableData' is assigned a value but never used.", "'hasGrossProfitMargin' is assigned a value but never used.", "'hasNetProfitMargin' is assigned a value but never used.", "'hasNetIncomeData' is assigned a value but never used.", ["533"], ["534"], "'hasValidData' is assigned a value but never used.", ["535"], "'variancePercent' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'shouldDisplayChart' and 'transformApiData'. Either include them or remove the dependency array.", ["536"], ["537"], ["538"], "'isNegativePriorYear' is assigned a value but never used.", ["539"], "'CircularProgress' is defined but never used.", "'SyncIcon' is defined but never used.", "'UploadIcon' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'minutes' is assigned a value but never used.", "'ampm' is assigned a value but never used.", "'displayHours' is assigned a value but never used.", "'Fade' is defined but never used.", "'Tooltip' is defined but never used.", "'InfoIcon' is defined but never used.", "'LaunchIcon' is defined but never used.", "'setShowBenefits' is assigned a value but never used.", "'theme' is assigned a value but never used.", "'Chip' is defined but never used.", "'Paper' is defined but never used.", "'Divider' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemIcon' is defined but never used.", "'ListItemText' is defined but never used.", "'AccountBalanceIcon' is defined but never used.", "'TrendingUpIcon' is defined but never used.", "'qboStatus' is assigned a value but never used.", {"desc": "540", "fix": "541"}, {"desc": "542", "fix": "543"}, {"desc": "544", "fix": "545"}, {"desc": "546", "fix": "547"}, {"desc": "548", "fix": "549"}, {"desc": "550", "fix": "551"}, {"desc": "552", "fix": "553"}, {"desc": "554", "fix": "555"}, {"desc": "556", "fix": "557"}, {"desc": "558", "fix": "559"}, {"desc": "560", "fix": "561"}, {"desc": "560", "fix": "562"}, {"desc": "560", "fix": "563"}, {"desc": "560", "fix": "564"}, "Update the dependencies array to be: [pdfUrl, report?.request_type, report.text]", {"range": "565", "text": "566"}, "Update the dependencies array to be: [page, rowsPerPage, refresh, searchTerm, sortConfig, fetchCompanies]", {"range": "567", "text": "568"}, "Update the dependencies array to be: [companyReports, currentCompanyId, reportDetail]", {"range": "569", "text": "570"}, "Update the dependencies array to be: [page, itemsPerPage, debouncedSearch, fetchUsers]", {"range": "571", "text": "572"}, "Update the dependencies array to be: [companyId, fetchContentSettings, initializeDataWithConnectionCheck, initializeSettings, reportId]", {"range": "573", "text": "574"}, "Update the dependencies array to be: [dataError, qboConnectionStatus, isCheckingConnection, startRedirectTimer, clearRedirectTimer]", {"range": "575", "text": "576"}, "Update the dependencies array to be: [reportData, contentSettings, isDataLoaded, initializeCharts]", {"range": "577", "text": "578"}, "Update the dependencies array to be: [fiscalData, contentSettings, isDataLoaded, initializeCharts]", {"range": "579", "text": "580"}, "Update the dependencies array to be: [operationalData, contentSettings, isDataLoaded, initializeCharts]", {"range": "581", "text": "582"}, "Update the dependencies array to be: [liquidityData, contentSettings, isDataLoaded, initializeCharts]", {"range": "583", "text": "584"}, "Update the dependencies array to be: [reportData, contentSettings, shouldDisplayChart, transformApiData]", {"range": "585", "text": "586"}, {"range": "587", "text": "586"}, {"range": "588", "text": "586"}, {"range": "589", "text": "586"}, [19774, 19796], "[pdfUrl, report?.request_type, report.text]", [4476, 4528], "[page, rowsPerPage, refresh, searchTerm, sortConfig, fetchCompanies]", [3923, 3955], "[companyReports, currentCompanyId, reportDetail]", [2176, 2213], "[page, itemsPerPage, debouncedSearch, fetchUsers]", [42820, 42841], "[companyId, fetchContentSettings, initializeDataWithConnectionCheck, initializeSettings, reportId]", [43414, 43468], "[dataError, qboConnectionStatus, isCheckingConnection, startRedirectTimer, clearRedirectTimer]", [2754, 2783], "[reportData, contentSettings, isDataLoaded, initializeCharts]", [2816, 2845], "[fiscalData, contentSettings, isDataLoaded, initializeCharts]", [2697, 2731], "[operationalData, contentSettings, isDataLoaded, initializeCharts]", [1318, 1350], "[liquidityData, contentSettings, isDataLoaded, initializeCharts]", [5532, 5561], "[reportData, contentSettings, shouldDisplayChart, transformApiData]", [5835, 5864], [3375, 3404], [8606, 8635]]