{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\FiscalYear.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from \"react\";\nimport ApexCharts from \"apexcharts\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FiscalYearDashboard = ({\n  headerTextStyle = {},\n  subHeadingTextStyle = {},\n  contentTextStyle = {},\n  fiscalData = null,\n  contentSettings = null // Add contentSettings prop\n}) => {\n  _s();\n  const stackedColumnRef = useRef(null);\n  const netIncomeRef = useRef(null);\n  const grossProfitRef = useRef(null);\n  const netProfitMarginRef = useRef(null);\n\n  // Enhanced data validation function - more lenient approach\n  const isDataLoaded = () => {\n    if (!fiscalData) {\n      return false;\n    }\n\n    // Check if at least one of the required data arrays exists and has content\n    const hasMonthlyData = fiscalData.monthlyPerformanceBreakDown && Array.isArray(fiscalData.monthlyPerformanceBreakDown) && fiscalData.monthlyPerformanceBreakDown.length > 0;\n    const hasGrossProfitMargin = fiscalData.monthlyGrossProfitMargin && Array.isArray(fiscalData.monthlyGrossProfitMargin) && fiscalData.monthlyGrossProfitMargin.length > 0;\n    const hasNetProfitMargin = fiscalData.nerProfitMargin && Array.isArray(fiscalData.nerProfitMargin) && fiscalData.nerProfitMargin.length > 0;\n    const hasNetIncomeData = fiscalData.netIncomeLoss && Array.isArray(fiscalData.netIncomeLoss) && fiscalData.netIncomeLoss.length > 0;\n    // We'll handle missing other data gracefully in the charts\n    return hasMonthlyData;\n  };\n\n  // Function to check if we have any meaningful data (not all zeros)\n  // const hasAnyUsableData = () => {\n  //   if (!fiscalData?.monthlyPerformanceBreakDown) {\n  //     return false;\n  //   }\n\n  //   const monthlyData = fiscalData.monthlyPerformanceBreakDown;\n\n  //   // Check if any monthly data has meaningful values\n  //   const hasMeaningfulMonthlyData = monthlyData.some(item => {\n  //     const income = parseFloat(item.totalIncome || 0);\n  //     const cogs = parseFloat(item.totalCOGS || 0);\n  //     const expenses = parseFloat(item.totalExpenses || 0);\n\n  //     return income > 0 || cogs > 0 || expenses > 0;\n  //   });\n  //   return hasMeaningfulMonthlyData;\n  // };\n\n  // Function to check if a chart should be displayed based on content settings\n  const shouldDisplayChart = chartKey => {\n    if (!(contentSettings !== null && contentSettings !== void 0 && contentSettings.chartSettings)) return true; // Default to true if no settings\n    return contentSettings.chartSettings[chartKey] === true;\n  };\n  useEffect(() => {\n    if (isDataLoaded()) {\n      // Clear charts first\n      [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach(ref => {\n        if (ref.current) {\n          ref.current.innerHTML = \"\";\n        }\n      });\n      // Initialize charts with new data\n      initializeCharts();\n    }\n  }, [fiscalData, contentSettings]); // Add contentSettings to dependency array\n\n  const formatMonthYear = (year, month) => {\n    const monthNames = [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"];\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\n  };\n  function formatNumber(num) {\n    // Round to 2 decimal places to fix floating point precision issues\n    const roundedNum = Math.round(num * 100) / 100;\n    const isNegative = roundedNum < 0;\n    const absNum = Math.abs(roundedNum);\n\n    // For numbers under 10k, show with appropriate decimal places (no suffix)\n    if (absNum < 1000) {\n      return (isNegative ? \"-\" : \"\") + (absNum % 1 === 0 ? absNum.toString() : absNum.toFixed(2));\n    }\n    const suffixes = [{\n      value: 1e12,\n      suffix: \"T\"\n    }, {\n      value: 1e9,\n      suffix: \"B\"\n    }, {\n      value: 1e6,\n      suffix: \"M\"\n    }, {\n      value: 1e3,\n      suffix: \"K\"\n    }];\n    for (let i = 0; i < suffixes.length; i++) {\n      if (absNum >= suffixes[i].value) {\n        const formatted = (absNum / suffixes[i].value).toFixed(1);\n        const cleanFormatted = formatted.endsWith(\".0\") ? formatted.slice(0, -2) : formatted;\n        return (isNegative ? \"-\" : \"\") + cleanFormatted + suffixes[i].suffix;\n      }\n    }\n    return (isNegative ? \"-\" : \"\") + roundedNum.toString();\n  }\n  const initializeCharts = () => {\n    if (!(fiscalData !== null && fiscalData !== void 0 && fiscalData.monthlyPerformanceBreakDown)) return;\n    const monthlyData = fiscalData.monthlyPerformanceBreakDown;\n\n    // Create categories from monthlyPerformanceBreakDown\n    const categories = monthlyData.map(item => formatMonthYear(item.year, item.month));\n\n    // Create lookup maps with fallback for missing data\n    const netProfitMarginMap = new Map();\n    if (fiscalData.nerProfitMargin && Array.isArray(fiscalData.nerProfitMargin)) {\n      fiscalData.nerProfitMargin.forEach(item => {\n        const key = `${item.year}-${item.month}`;\n        const value = parseFloat(item.nerProfitMargin) || 0;\n        netProfitMarginMap.set(key, value);\n      });\n    }\n    const grossProfitMarginMap = new Map();\n    if (fiscalData.monthlyGrossProfitMargin && Array.isArray(fiscalData.monthlyGrossProfitMargin)) {\n      fiscalData.monthlyGrossProfitMargin.forEach(item => {\n        const key = `${item.year}-${item.month}`;\n        const value = parseFloat(item.Gross_Profit_Margin);\n        const percentage = value > 1 ? value : value * 100;\n        grossProfitMarginMap.set(key, percentage || 0);\n      });\n    }\n    const netIncomeMap = new Map();\n    if (fiscalData.netIncomeLoss && Array.isArray(fiscalData.netIncomeLoss)) {\n      fiscalData.netIncomeLoss.forEach(item => {\n        const key = `${item.year}-${item.month}`;\n        const value = parseFloat(item.netIncomeLoss) / 1000 || 0;\n        netIncomeMap.set(key, value);\n      });\n    }\n\n    // Create aligned data arrays\n    const incomeData = monthlyData.map(item => parseFloat(item.totalIncome) / 1000 || 0);\n    const cogsData = monthlyData.map(item => parseFloat(item.totalCOGS) / 1000 || 0);\n    const expenseData = monthlyData.map(item => parseFloat(item.totalExpenses) / 1000 || 0);\n    const grossProfitMarginData = monthlyData.map(item => {\n      const key = `${item.year}-${item.month}`;\n      return grossProfitMarginMap.get(key) || 0;\n    });\n    const netProfitMarginRaw = monthlyData.map(item => {\n      const key = `${item.year}-${item.month}`;\n      return netProfitMarginMap.get(key) || 0;\n    });\n    const netIncomeData = monthlyData.map(item => {\n      const key = `${item.year}-${item.month}`;\n      return netIncomeMap.get(key) || 0;\n    });\n\n    // 1. Stacked Column Chart (incomeSummary)\n    const stackedColumnOptions = {\n      series: [{\n        name: \"Income\",\n        type: \"line\",\n        data: incomeData\n      }, {\n        name: \"Expense\",\n        type: \"column\",\n        data: expenseData\n      }, {\n        name: \"Cost of Goods Sold\",\n        type: \"column\",\n        data: cogsData\n      }],\n      chart: {\n        height: 450,\n        type: \"bar\",\n        stacked: true,\n        toolbar: {\n          show: false\n        },\n        background: \"transparent\"\n      },\n      dataLabels: {\n        enabled: true,\n        enabledOnSeries: [0, 1, 2],\n        // Show labels on all three series: Income, Expense, and COGS\n        formatter: function (val, opts) {\n          if (val === null || val === undefined || isNaN(val)) return \"\";\n          const absVal = Math.abs(val);\n          if (absVal >= 1000) {\n            return '$' + (val / 1000).toFixed(1) + 'm';\n          } else if (absVal >= 1) {\n            return '$' + val.toFixed(2) + 'k';\n          } else {\n            return '$' + (val * 1000).toFixed(0);\n          }\n        },\n        style: {\n          fontSize: \"14px\",\n          colors: [\"#20b2aa\", \"#333\", \"#333\"],\n          // Different colors for each series\n          fontWeight: \"500\"\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        width: [2, 0, 0],\n        curve: \"smooth\"\n      },\n      plotOptions: {\n        bar: {\n          columnWidth: \"60%\",\n          dataLabels: {\n            total: {\n              enabled: false,\n              // Disable total labels\n              offsetY: -20,\n              style: {\n                fontSize: \"14px\",\n                fontWeight: \"500\",\n                color: \"#333\"\n              },\n              formatter: function (val) {\n                if (val === null || val === undefined || isNaN(val)) return \"$0\";\n                const absVal = Math.abs(val);\n                if (absVal >= 1000) {\n                  return '$' + (val / 1000).toFixed(1) + 'm';\n                } else if (absVal >= 1) {\n                  return '$' + val.toFixed(1) + 'k';\n                } else {\n                  return '$' + (val * 1000).toFixed(0);\n                }\n              }\n            }\n          }\n        }\n      },\n      fill: {\n        opacity: [1, 1, 1] // Make all series fully opaque\n      },\n      labels: categories,\n      markers: {\n        size: [5, 0, 0],\n        fontSize: \"14px\",\n        strokeColors: \"#fff\",\n        strokeWidth: 2,\n        fillOpacity: 1,\n        hover: {\n          size: 7\n        }\n      },\n      xaxis: {\n        labels: {\n          style: {\n            colors: \"#666\",\n            fontSize: \"14px\"\n          },\n          offsetY: 15 // Push month labels down by 15px\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false,\n        // More conservative scaling to ensure small COGS values are visible\n        min: 0,\n        max: function () {\n          // Calculate max considering stacked values\n          const maxStacked = Math.max(...cogsData.map((cogs, i) => cogs + expenseData[i]));\n          const maxIncome = Math.max(...incomeData);\n          return Math.max(maxStacked, maxIncome) * 1.2;\n        }\n      },\n      colors: [\"#20b2aa\", \"#ff8a80\", \"#53579f\"],\n      // Colors for Income, Expense, COGS\n      legend: {\n        position: \"bottom\",\n        horizontalAlign: \"center\",\n        fontSize: \"14px\",\n        fontWeight: \"400\",\n        markers: {\n          width: 12,\n          height: 12,\n          radius: 6 // Circular markers\n        },\n        labels: {\n          colors: \"#333\",\n          useSeriesColors: false\n        },\n        itemMargin: {\n          horizontal: 15,\n          vertical: 4\n        },\n        offsetY: 10,\n        onItemClick: {\n          toggleDataSeries: true // Enable clicking to hide/show series\n        },\n        onItemHover: {\n          highlightDataSeries: true // Enable hover highlighting\n        }\n      },\n      tooltip: {\n        shared: true,\n        intersect: false,\n        // Custom tooltip to clearly show all values\n        custom: function ({\n          series,\n          seriesIndex,\n          dataPointIndex,\n          w\n        }) {\n          const income = (series[0][dataPointIndex] * 1000).toFixed(0);\n          const expense = (series[1][dataPointIndex] * 1000).toFixed(0);\n          const cogs = (series[2][dataPointIndex] * 1000).toFixed(0);\n          const category = w.globals.labels[dataPointIndex];\n          return `\n        <div style=\"padding: 12px; background: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); font-size: 14px;\">\n          <div style=\"font-weight: 600; margin-bottom: 8px; color: #333;\">${category}</div>\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\n            <div style=\"width: 12px; height: 12px; background: #20b2aa; border-radius: 50%; margin-right: 8px;\"></div>\n            <span style=\"color: #333;\">Income: <strong>$${income}</strong></span>\n          </div>\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\n            <div style=\"width: 12px; height: 12px; background: #ff8a80; border-radius: 50%; margin-right: 8px;\"></div>\n            <span style=\"color: #333;\">Expense: <strong>$${expense}</strong></span>\n          </div>\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px; ${cogs == 0 ? 'opacity: 0.6;' : ''}\">\n            <div style=\"width: 12px; height: 12px; background: #4361ee; border-radius: 50%; margin-right: 8px;\"></div>\n            <span style=\"color: #333;\">COGS: <strong>$${cogs}</strong> ${cogs == 0 ? '(No data)' : ''}</span>\n          </div>\n          <div style=\"margin-top: 8px; padding-top: 8px; border-top: 1px solid #eee; font-size: 12px; color: #666;\">\n            Total Costs: <strong>$${(parseFloat(expense) + parseFloat(cogs)).toFixed(0)}</strong>\n          </div>\n        </div>\n      `;\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 30 // Increased bottom padding from 0 to 30\n        }\n      },\n      // Add annotations to highlight if COGS data exists\n      annotations: {\n        yaxis: cogsData.some(val => val > 0) ? [] : [{\n          y: 0,\n          borderColor: '#FF4560',\n          label: {\n            borderColor: '#FF4560',\n            style: {\n              color: '#fff',\n              background: '#FF4560'\n            }\n          }\n        }]\n      }\n    };\n\n    // 2. Net Income Chart\n    const netIncomeOptions = {\n      series: [{\n        name: 'Net Income',\n        data: netIncomeData\n      }],\n      chart: {\n        type: 'line',\n        height: 300,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent',\n        zoom: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          if (val >= 1) {\n            return '$' + val.toFixed(2) + 'k';\n          } else if (val >= 0.1) {\n            return '$' + val.toFixed(2) + 'k';\n          } else if (val <= -0.1) {\n            return '-$' + Math.abs(val).toFixed(2) + 'k';\n          } else {\n            return '$' + val.toFixed(0) + 'k';\n          }\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -15,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'straight',\n        width: 3\n      },\n      fill: {\n        type: 'solid'\n      },\n      markers: {\n        size: 5,\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        colors: netIncomeData.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\n        hover: {\n          size: 7\n        },\n        discrete: netIncomeData.map((val, index) => ({\n          seriesIndex: 0,\n          dataPointIndex: index,\n          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\n          strokeColor: '#fff',\n          size: 5\n        }))\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: ['#1E7C8C'],\n      // Default color for positive values\n      plotOptions: {\n        line: {\n          colors: {\n            threshold: 0,\n            colorAboveThreshold: '#1E7C8C',\n            // Teal for positive values\n            colorBelowThreshold: '#d70015' // Red for negative values\n          }\n        }\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            if (val >= 1) {\n              return '$' + val.toFixed(2) + ' k';\n            } else if (val >= 0.1) {\n              return '$' + val.toFixed(2) + ' k';\n            } else if (val <= -0.1) {\n              return '-$' + Math.abs(val).toFixed(2) + ' k';\n            } else {\n              return '$' + val.toFixed(0) + ' k';\n            }\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 25,\n          bottom: 0\n        }\n      },\n      annotations: {\n        yaxis: [{\n          y: 0,\n          borderColor: '#666',\n          borderWidth: 1,\n          strokeDashArray: 0,\n          opacity: 0.8\n        }]\n      }\n    };\n\n    // 3. Gross Profit Margin Chart\n    const grossProfitOptions = {\n      series: [{\n        name: \"Gross Profit Margin\",\n        data: grossProfitMarginData\n      }],\n      chart: {\n        type: \"bar\",\n        height: 350,\n        toolbar: {\n          show: false\n        },\n        background: \"transparent\"\n      },\n      plotOptions: {\n        bar: {\n          horizontal: false,\n          columnWidth: \"55%\",\n          endingShape: \"rounded\",\n          dataLabels: {\n            position: \"top\"\n          }\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        position: 'top',\n        formatter: function (val) {\n          if (val >= 1) {\n            return val.toFixed(2) + '%';\n          } else if (val >= 0.1) {\n            return val.toFixed(2) + '%';\n          } else if (val <= -0.1) {\n            return '-' + Math.abs(val).toFixed(2) + '%';\n          } else {\n            return val.toFixed(0) + '%';\n          }\n        },\n        offsetY: -20,\n        style: {\n          fontSize: \"14px\",\n          colors: [\"#333\"],\n          fontWeight: \"500\"\n        }\n      },\n      stroke: {\n        show: true,\n        width: 2,\n        colors: [\"transparent\"]\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: \"#666\",\n            fontSize: \"14px\"\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false,\n        min: Math.min(...grossProfitMarginData) < 0 ? Math.min(...grossProfitMarginData) * 1.1 : 0,\n        max: Math.max(...grossProfitMarginData) * 1.2\n      },\n      fill: {\n        opacity: 1\n      },\n      colors: [\"#4a4a9a\"],\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            if (val === null || val === undefined || isNaN(val)) return \"N/A\";\n            return val.toFixed(2) + \"%\";\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      }\n    };\n\n    // 4. Net Profit Margin Chart\n    const netProfitMarginOptions = {\n      series: [{\n        name: 'Net Profit Margin',\n        data: netProfitMarginRaw\n      }],\n      chart: {\n        type: 'line',\n        height: 300,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent',\n        zoom: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          if (val >= 1) {\n            return val.toFixed(2) + '%';\n          } else if (val >= 0.1) {\n            return val.toFixed(2) + '%';\n          } else if (val <= -0.1) {\n            return '-' + Math.abs(val).toFixed(2) + '%';\n          } else {\n            return val.toFixed(0) + '%';\n          }\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -15,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'straight',\n        width: 3\n      },\n      fill: {\n        type: 'solid'\n      },\n      markers: {\n        size: 5,\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        colors: netProfitMarginRaw.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\n        hover: {\n          size: 7\n        },\n        discrete: netProfitMarginRaw.map((val, index) => ({\n          seriesIndex: 0,\n          dataPointIndex: index,\n          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\n          strokeColor: '#fff',\n          size: 5\n        }))\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: ['#1E7C8C'],\n      // Default line color\n      plotOptions: {\n        line: {\n          colors: {\n            threshold: 0,\n            colorAboveThreshold: '#1E7C8C',\n            colorBelowThreshold: '#d70015'\n          }\n        }\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            if (val >= 1) {\n              return val.toFixed(2) + '%';\n            } else if (val >= 0.1) {\n              return val.toFixed(2) + '%';\n            } else if (val <= -0.1) {\n              return '-' + Math.abs(val).toFixed(2) + '%';\n            } else {\n              return val.toFixed(0) + '%';\n            }\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 25,\n          bottom: 0\n        }\n      },\n      annotations: {\n        yaxis: [{\n          y: 0,\n          borderColor: '#666',\n          borderWidth: 1,\n          strokeDashArray: 0,\n          opacity: 0.8\n        }]\n      }\n    };\n\n    // Clear existing charts\n    [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach(ref => {\n      if (ref.current) {\n        ref.current.innerHTML = \"\";\n      }\n    });\n\n    // Get enabled charts and assign chart options\n    const enabledCharts = getEnabledCharts();\n\n    // Assign chart options to enabled charts\n    enabledCharts.forEach(chart => {\n      switch (chart.key) {\n        case 'incomeSummary':\n          chart.options = stackedColumnOptions;\n          chart.name = 'Stacked Column';\n          break;\n        case 'netIncome':\n          chart.options = netIncomeOptions;\n          chart.name = 'Net Income';\n          break;\n        case 'grossProfitMargin':\n          chart.options = grossProfitOptions;\n          chart.name = 'Gross Profit Margin';\n          break;\n        case 'netProfitMargin':\n          chart.options = netProfitMarginOptions;\n          chart.name = 'Net Profit Margin';\n          break;\n      }\n    });\n\n    // Clear existing charts before rendering new ones\n    const clearAndRenderChart = (ref, options, chartName) => {\n      if (ref.current) {\n        // Clear any existing chart\n        ref.current.innerHTML = '';\n\n        // Wait a tick before rendering to ensure DOM is cleared\n        setTimeout(() => {\n          if (ref.current) {\n            try {\n              const chart = new ApexCharts(ref.current, options);\n              chart.render();\n\n              // Store chart instances globally for export\n              if (chartName === \"Stacked Column\") {\n                window.stackedColumnChart = chart;\n              } else if (chartName === \"Net Income\") {\n                window.netIncomeChart = chart;\n              } else if (chartName === \"Gross Profit Margin\") {\n                window.grossProfitChart = chart;\n              } else if (chartName === \"Net Profit Margin\") {\n                window.netProfitMarginChart = chart;\n              }\n            } catch (error) {\n              console.error(`FiscalYear - Error rendering ${chartName} chart:`, error);\n              // Show error message in chart container\n              ref.current.innerHTML = `<div class=\"flex items-center justify-center h-48 text-gray-500\">Error loading ${chartName} chart</div>`;\n            }\n          }\n        }, 10);\n      }\n    };\n\n    // Helper function to check if data array has meaningful values (not all zeros)\n    const hasMeaningfulData = dataArray => {\n      return dataArray && dataArray.length > 0 && dataArray.some(val => parseFloat(val) !== 0);\n    };\n\n    // Render charts with fallback for empty/zero data\n    enabledCharts.forEach(({\n      ref,\n      options,\n      name,\n      key\n    }) => {\n      if (ref.current) {\n        let hasData = false;\n\n        // Check if chart has meaningful data based on chart type\n        if (key === 'incomeSummary') {\n          const monthlyData = (fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.monthlyPerformanceBreakDown) || [];\n          hasData = monthlyData.some(item => {\n            const income = parseFloat(item.totalIncome || 0);\n            const cogs = parseFloat(item.totalCOGS || 0);\n            const expenses = parseFloat(item.totalExpenses || 0);\n            return income !== 0 || cogs !== 0 || expenses !== 0;\n          });\n        } else if (key === 'netIncome') {\n          var _fiscalData$netIncome;\n          const netIncomeData = (fiscalData === null || fiscalData === void 0 ? void 0 : (_fiscalData$netIncome = fiscalData.netIncomeLoss) === null || _fiscalData$netIncome === void 0 ? void 0 : _fiscalData$netIncome.map(item => parseFloat(item.netIncomeLoss || 0))) || [];\n          hasData = hasMeaningfulData(netIncomeData);\n        } else if (key === 'grossProfitMargin') {\n          var _fiscalData$monthlyGr;\n          const grossProfitData = (fiscalData === null || fiscalData === void 0 ? void 0 : (_fiscalData$monthlyGr = fiscalData.monthlyGrossProfitMargin) === null || _fiscalData$monthlyGr === void 0 ? void 0 : _fiscalData$monthlyGr.map(item => parseFloat(item.Gross_Profit_Margin || 0))) || [];\n          hasData = hasMeaningfulData(grossProfitData);\n        } else if (key === 'netProfitMargin') {\n          var _fiscalData$nerProfit;\n          const netProfitData = (fiscalData === null || fiscalData === void 0 ? void 0 : (_fiscalData$nerProfit = fiscalData.nerProfitMargin) === null || _fiscalData$nerProfit === void 0 ? void 0 : _fiscalData$nerProfit.map(item => parseFloat(item.nerProfitMargin || 0))) || [];\n          hasData = hasMeaningfulData(netProfitData);\n        }\n        if (hasData) {\n          clearAndRenderChart(ref, options, name);\n        } else {\n          ref.current.innerHTML = `<div class=\"flex items-center justify-center h-64 text-gray-500\">No meaningful ${name.toLowerCase()} data available</div>`;\n        }\n      }\n    });\n\n    // Clear all chart containers that are not being used\n    [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach(ref => {\n      if (ref.current && !enabledCharts.some(chart => chart.ref === ref)) {\n        ref.current.innerHTML = \"\";\n      }\n    });\n  };\n  const formatHeaderPeriod = (startYear, startMonth) => {\n    const monthNames = [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\n    if (!startYear || !startMonth) {\n      return \" \"; // fallback\n    }\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\n\n    return `${startMonthName} ${startYear}`;\n  };\n  const formatHeaderStyle = () => {\n    const style = {\n      ...headerTextStyle\n    };\n    if (style.fontSize) {\n      const fontSize = parseInt(style.fontSize);\n      style.fontSize = `${fontSize / 2}px`;\n    }\n    return style;\n  };\n  const formatCompanyName = companyName => {\n    if (!companyName) return '';\n    if (companyName.length > 15) {\n      return companyName.substring(0, 15) + '...';\n    }\n    return companyName;\n  };\n\n  // Calculate YTD totals from monthly data\n  const calculateYTDTotals = () => {\n    if (!fiscalData.monthlyPerformanceBreakDown) return {};\n    return fiscalData.monthlyPerformanceBreakDown.reduce((totals, month) => {\n      totals.totalIncome += parseFloat(month.totalIncome || 0);\n      totals.totalCOGS += parseFloat(month.totalCOGS || 0);\n      totals.totalExpenses += parseFloat(month.totalExpenses || 0);\n      return totals;\n    }, {\n      totalIncome: 0,\n      totalCOGS: 0,\n      totalExpenses: 0\n    });\n  };\n\n  // Function to determine which charts should be rendered and their order\n  const getEnabledCharts = () => {\n    const allCharts = [{\n      key: 'incomeSummary',\n      title: 'Monthly Performance Breakdown',\n      ref: stackedColumnRef,\n      options: null,\n      // Will be set in initializeCharts\n      hasData: () => {\n        if (!(fiscalData !== null && fiscalData !== void 0 && fiscalData.monthlyPerformanceBreakDown)) return false;\n        const monthlyData = fiscalData.monthlyPerformanceBreakDown;\n        return monthlyData.some(item => {\n          const income = parseFloat(item.totalIncome || 0);\n          const cogs = parseFloat(item.totalCOGS || 0);\n          const expenses = parseFloat(item.totalExpenses || 0);\n          return income > 0 || cogs > 0 || expenses > 0;\n        });\n      }\n    }, {\n      key: 'netIncome',\n      title: 'Net Income/(Loss)',\n      ref: netIncomeRef,\n      options: null,\n      hasData: () => {\n        if (!(fiscalData !== null && fiscalData !== void 0 && fiscalData.netIncomeLoss)) return false;\n        return fiscalData.netIncomeLoss.some(item => parseFloat(item.netIncomeLoss || 0) !== 0);\n      }\n    }, {\n      key: 'grossProfitMargin',\n      title: 'Gross Profit Margin',\n      ref: grossProfitRef,\n      options: null,\n      hasData: () => {\n        if (!(fiscalData !== null && fiscalData !== void 0 && fiscalData.monthlyGrossProfitMargin)) return false;\n        return fiscalData.monthlyGrossProfitMargin.some(item => parseFloat(item.Gross_Profit_Margin || 0) !== 0);\n      },\n      hasDescription: true,\n      description: {\n        title: 'Gross Profit Margin',\n        content: 'Is a share of Gross Profit in Total Income or the profit left for covering operating and other expenses. A good Gross Profit Margin is high enough to cover overhead and leave a reasonable Net Profit.'\n      }\n    }, {\n      key: 'netProfitMargin',\n      title: 'Net Profit Margin',\n      ref: netProfitMarginRef,\n      options: null,\n      hasData: () => {\n        if (!(fiscalData !== null && fiscalData !== void 0 && fiscalData.nerProfitMargin)) return false;\n        return fiscalData.nerProfitMargin.some(item => parseFloat(item.nerProfitMargin || 0) !== 0);\n      },\n      hasDescription: true,\n      description: {\n        title: 'Net Profit Margin',\n        content: 'Shows the profit earned per dollar of income. A 10% Net Profit Margin is considered an excellent ratio. If your company has a low Net Profit Margin you are making very little profit after all costs. That implies the revenue is getting eaten up by expenses. It also increases the risk your firm will be unable to meet obligations. With a low margin, a sudden dip in sales over the next month or year could turn your company unprofitable. A high margin indicates your company has solid competitive advantages.'\n      }\n    }];\n\n    // Filter charts based on settings and data availability\n    return allCharts.filter(chart => shouldDisplayChart(chart.key));\n  };\n  const ytdTotals = calculateYTDTotals();\n  // Fix floating point precision for net profit calculation\n  const netProfit = Math.round((ytdTotals.totalIncome - ytdTotals.totalCOGS - ytdTotals.totalExpenses) * 100) / 100;\n\n  // Get enabled charts for dynamic layout\n  const enabledCharts = getEnabledCharts();\n\n  // Split charts between upper and lower divs\n  // Upper div can hold up to 2 charts, lower div gets the rest\n  const upperDivCharts = enabledCharts.slice(0, 2);\n  const lowerDivCharts = enabledCharts.slice(2);\n\n  // Helper function to render a chart component\n  const renderChart = chart => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white p-6 border-b-4 border-blue-900 mb-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-2xl font-semibold text-teal-600 mb-3\",\n      style: subHeadingTextStyle,\n      children: chart.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 947,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: chart.ref\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 953,\n      columnNumber: 7\n    }, this), chart.hasDescription && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-teal-600 text-2xl\",\n        style: {\n          ...subHeadingTextStyle,\n          fontWeight: \"lighter\"\n        },\n        children: chart.description.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 956,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: contentTextStyle,\n        children: chart.description.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 962,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 955,\n      columnNumber: 9\n    }, this)]\n  }, chart.key, true, {\n    fileName: _jsxFileName,\n    lineNumber: 946,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[400mm] mx-auto bg-white flex flex-col gap-3 p-10 mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4  border-b-4 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Current Fiscal Year\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 975,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: formatHeaderStyle(),\n          children: [formatHeaderPeriod(fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.FYEndYear, fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.FYStartMonth), \" | \", formatCompanyName(fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.companyName)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 981,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 974,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metrics-flex grid grid-cols-4 gap-5 pb-6 pt-3 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl mb-1\",\n            style: {\n              ...contentTextStyle,\n              fontSize: '20px',\n              color: \"#4b5562\"\n            },\n            children: \"YTD Total Income\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 989,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl font-bold text-gray-600 m-0\",\n            children: formatNumber(ytdTotals.totalIncome)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 992,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 988,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 text-center\",\n          style: {\n            backgroundColor: \"#d2e9ea\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl mb-1\",\n            style: {\n              ...contentTextStyle,\n              fontSize: '20px',\n              color: \"#4b5562\"\n            },\n            children: \"YTD Cost of Goods Sold\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1000,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl font-bold text-gray-600 m-0\",\n            children: formatNumber(ytdTotals.totalCOGS)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1003,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 996,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl mb-1\",\n            style: {\n              ...contentTextStyle,\n              fontSize: '20px',\n              color: \"#4b5562\"\n            },\n            children: \"YTD Total Expense\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1008,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl font-bold text-gray-600 m-0\",\n            children: formatNumber(ytdTotals.totalExpenses)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1011,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1007,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 text-center\",\n          style: {\n            backgroundColor: \"#d2e9ea\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl mb-1\",\n            style: {\n              ...contentTextStyle,\n              fontSize: '20px',\n              color: \"#4b5562\"\n            },\n            children: \"YTD Net Profit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1019,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl font-bold text-gray-600 m-0\",\n            children: formatNumber(netProfit)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1022,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1015,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 987,\n        columnNumber: 9\n      }, this), upperDivCharts.map(chart => renderChart(chart))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 972,\n      columnNumber: 7\n    }, this), lowerDivCharts.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-4 p-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pt-2 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Current Fiscal Year\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1037,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: formatHeaderStyle(),\n          children: [formatHeaderPeriod(fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.FYEndYear, fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.FYStartMonth), \" | \", formatCompanyName(fiscalData === null || fiscalData === void 0 ? void 0 : fiscalData.companyName)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1043,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1036,\n        columnNumber: 11\n      }, this), lowerDivCharts.map(chart => renderChart(chart))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1035,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 971,\n    columnNumber: 5\n  }, this);\n};\n_s(FiscalYearDashboard, \"TNVw/Nsc0yyRtbTo+qU1kNvpirA=\");\n_c = FiscalYearDashboard;\nexport default FiscalYearDashboard;\nvar _c;\n$RefreshReg$(_c, \"FiscalYearDashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Apex<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "FiscalYearDashboard", "headerTextStyle", "subHeadingTextStyle", "contentTextStyle", "fiscalData", "contentSettings", "_s", "stackedColumnRef", "netIncomeRef", "grossProfitRef", "netProfitMarginRef", "isDataLoaded", "hasMonthlyData", "monthlyPerformanceBreakDown", "Array", "isArray", "length", "hasGrossProfitMargin", "monthlyGrossProfitMargin", "hasNetProfitMargin", "nerProfitMargin", "hasNetIncomeData", "netIncomeLoss", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chart<PERSON>ey", "chartSettings", "for<PERSON>ach", "ref", "current", "innerHTML", "initializeCharts", "formatMonthYear", "year", "month", "monthNames", "String", "slice", "formatNumber", "num", "roundedNum", "Math", "round", "isNegative", "absNum", "abs", "toString", "toFixed", "suffixes", "value", "suffix", "i", "formatted", "cleanFormatted", "endsWith", "monthlyData", "categories", "map", "item", "netProfitMarginMap", "Map", "key", "parseFloat", "set", "grossProfitMarginMap", "Gross_Profit_Margin", "percentage", "netIncomeMap", "incomeData", "totalIncome", "cogsData", "totalCOGS", "expenseData", "totalExpenses", "grossProfitMarginData", "get", "netProfitMarginRaw", "netIncomeData", "stackedColumnOptions", "series", "name", "type", "data", "chart", "height", "stacked", "toolbar", "show", "background", "dataLabels", "enabled", "enabledOnSeries", "formatter", "val", "opts", "undefined", "isNaN", "absVal", "style", "fontSize", "colors", "fontWeight", "offsetY", "dropShadow", "stroke", "width", "curve", "plotOptions", "bar", "columnWidth", "total", "color", "fill", "opacity", "labels", "markers", "size", "strokeColors", "strokeWidth", "fillOpacity", "hover", "xaxis", "axisBorder", "axisTicks", "yaxis", "min", "max", "maxStacked", "cogs", "max<PERSON><PERSON><PERSON>", "legend", "position", "horizontalAlign", "radius", "useSeriesColors", "itemMargin", "horizontal", "vertical", "onItemClick", "toggleDataSeries", "onItemHover", "highlightDataSeries", "tooltip", "shared", "intersect", "custom", "seriesIndex", "dataPointIndex", "w", "income", "expense", "category", "globals", "grid", "padding", "left", "right", "top", "bottom", "annotations", "some", "y", "borderColor", "label", "netIncomeOptions", "zoom", "discrete", "index", "fillColor", "strokeColor", "line", "threshold", "colorAboveThreshold", "colorBelowThreshold", "borderWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "grossProfitOptions", "endingShape", "netProfitMarginOptions", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "options", "clearAndRender<PERSON>hart", "chartName", "setTimeout", "render", "window", "stackedColumnChart", "netIncomeChart", "grossProfitChart", "netProfitMarginChart", "error", "console", "hasMeaningfulData", "dataArray", "hasData", "expenses", "_fiscalData$netIncome", "_fiscalData$monthlyGr", "grossProfitData", "_fiscalData$nerProfit", "netProfitData", "toLowerCase", "formatHeaderPeriod", "startYear", "startMonth", "startMonthName", "formatHeaderStyle", "parseInt", "formatCompanyName", "companyName", "substring", "calculateYTDTotals", "reduce", "totals", "all<PERSON>hart<PERSON>", "title", "hasDescription", "description", "content", "filter", "ytdTotals", "netProfit", "upperDivCharts", "lowerDiv<PERSON><PERSON>s", "<PERSON><PERSON><PERSON>", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "FYEndYear", "FYStartMonth", "backgroundColor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/reports/ReportPages/FiscalYear.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from \"react\";\r\nimport ApexCharts from \"apexcharts\";\r\n\r\nconst FiscalYearDashboard = ({\r\n  headerTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  contentTextStyle = {},\r\n  fiscalData = null,\r\n  contentSettings = null, // Add contentSettings prop\r\n}) => {\r\n  const stackedColumnRef = useRef(null);\r\n  const netIncomeRef = useRef(null);\r\n  const grossProfitRef = useRef(null);\r\n  const netProfitMarginRef = useRef(null);\r\n\r\n  // Enhanced data validation function - more lenient approach\r\n  const isDataLoaded = () => {\r\n    if (!fiscalData) {\r\n      return false;\r\n    }\r\n\r\n    // Check if at least one of the required data arrays exists and has content\r\n    const hasMonthlyData = fiscalData.monthlyPerformanceBreakDown &&\r\n      Array.isArray(fiscalData.monthlyPerformanceBreakDown) &&\r\n      fiscalData.monthlyPerformanceBreakDown.length > 0;\r\n\r\n    const hasGrossProfitMargin = fiscalData.monthlyGrossProfitMargin &&\r\n      Array.isArray(fiscalData.monthlyGrossProfitMargin) &&\r\n      fiscalData.monthlyGrossProfitMargin.length > 0;\r\n\r\n    const hasNetProfitMargin = fiscalData.nerProfitMargin &&\r\n      Array.isArray(fiscalData.nerProfitMargin) &&\r\n      fiscalData.nerProfitMargin.length > 0;\r\n\r\n    const hasNetIncomeData = fiscalData.netIncomeLoss &&\r\n      Array.isArray(fiscalData.netIncomeLoss) &&\r\n      fiscalData.netIncomeLoss.length > 0;\r\n    // We'll handle missing other data gracefully in the charts\r\n    return hasMonthlyData;\r\n  };\r\n\r\n  // Function to check if we have any meaningful data (not all zeros)\r\n  // const hasAnyUsableData = () => {\r\n  //   if (!fiscalData?.monthlyPerformanceBreakDown) {\r\n  //     return false;\r\n  //   }\r\n\r\n  //   const monthlyData = fiscalData.monthlyPerformanceBreakDown;\r\n\r\n  //   // Check if any monthly data has meaningful values\r\n  //   const hasMeaningfulMonthlyData = monthlyData.some(item => {\r\n  //     const income = parseFloat(item.totalIncome || 0);\r\n  //     const cogs = parseFloat(item.totalCOGS || 0);\r\n  //     const expenses = parseFloat(item.totalExpenses || 0);\r\n\r\n  //     return income > 0 || cogs > 0 || expenses > 0;\r\n  //   });\r\n  //   return hasMeaningfulMonthlyData;\r\n  // };\r\n\r\n  // Function to check if a chart should be displayed based on content settings\r\n  const shouldDisplayChart = (chartKey) => {\r\n    if (!contentSettings?.chartSettings) return true; // Default to true if no settings\r\n    return contentSettings.chartSettings[chartKey] === true;\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (isDataLoaded()) {\r\n      // Clear charts first\r\n      [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach((ref) => {\r\n        if (ref.current) {\r\n          ref.current.innerHTML = \"\";\r\n        }\r\n      });\r\n      // Initialize charts with new data\r\n      initializeCharts();\r\n    }\r\n  }, [fiscalData, contentSettings]); // Add contentSettings to dependency array\r\n\r\n  const formatMonthYear = (year, month) => {\r\n    const monthNames = [\r\n      \"Jan\",\r\n      \"Feb\",\r\n      \"Mar\",\r\n      \"Apr\",\r\n      \"May\",\r\n      \"Jun\",\r\n      \"Jul\",\r\n      \"Aug\",\r\n      \"Sep\",\r\n      \"Oct\",\r\n      \"Nov\",\r\n      \"Dec\",\r\n    ];\r\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\r\n  };\r\n\r\n  function formatNumber(num) {\r\n    // Round to 2 decimal places to fix floating point precision issues\r\n    const roundedNum = Math.round(num * 100) / 100;\r\n    const isNegative = roundedNum < 0;\r\n    const absNum = Math.abs(roundedNum);\r\n\r\n    // For numbers under 10k, show with appropriate decimal places (no suffix)\r\n    if (absNum < 1000) {\r\n      return (isNegative ? \"-\" : \"\") + (absNum % 1 === 0 ? absNum.toString() : absNum.toFixed(2));\r\n    }\r\n\r\n    const suffixes = [\r\n      { value: 1e12, suffix: \"T\" },\r\n      { value: 1e9, suffix: \"B\" },\r\n      { value: 1e6, suffix: \"M\" },\r\n      { value: 1e3, suffix: \"K\" },\r\n    ];\r\n\r\n    for (let i = 0; i < suffixes.length; i++) {\r\n      if (absNum >= suffixes[i].value) {\r\n        const formatted = (absNum / suffixes[i].value).toFixed(1);\r\n        const cleanFormatted = formatted.endsWith(\".0\")\r\n          ? formatted.slice(0, -2)\r\n          : formatted;\r\n        return (isNegative ? \"-\" : \"\") + cleanFormatted + suffixes[i].suffix;\r\n      }\r\n    }\r\n\r\n    return (isNegative ? \"-\" : \"\") + roundedNum.toString();\r\n  }\r\n\r\n  const initializeCharts = () => {\r\n    if (!fiscalData?.monthlyPerformanceBreakDown) return;\r\n\r\n    const monthlyData = fiscalData.monthlyPerformanceBreakDown;\r\n\r\n    // Create categories from monthlyPerformanceBreakDown\r\n    const categories = monthlyData.map((item) =>\r\n      formatMonthYear(item.year, item.month)\r\n    );\r\n\r\n    // Create lookup maps with fallback for missing data\r\n    const netProfitMarginMap = new Map();\r\n    if (fiscalData.nerProfitMargin && Array.isArray(fiscalData.nerProfitMargin)) {\r\n      fiscalData.nerProfitMargin.forEach(item => {\r\n        const key = `${item.year}-${item.month}`;\r\n        const value = parseFloat(item.nerProfitMargin) || 0;\r\n        netProfitMarginMap.set(key, value);\r\n      });\r\n    }\r\n\r\n    const grossProfitMarginMap = new Map();\r\n    if (fiscalData.monthlyGrossProfitMargin && Array.isArray(fiscalData.monthlyGrossProfitMargin)) {\r\n      fiscalData.monthlyGrossProfitMargin.forEach(item => {\r\n        const key = `${item.year}-${item.month}`;\r\n        const value = parseFloat(item.Gross_Profit_Margin);\r\n        const percentage = value > 1 ? value : value * 100;\r\n        grossProfitMarginMap.set(key, percentage || 0);\r\n      });\r\n    }\r\n\r\n    const netIncomeMap = new Map();\r\n    if (fiscalData.netIncomeLoss && Array.isArray(fiscalData.netIncomeLoss)) {\r\n      fiscalData.netIncomeLoss.forEach(item => {\r\n        const key = `${item.year}-${item.month}`;\r\n        const value = parseFloat(item.netIncomeLoss) / 1000 || 0;\r\n        netIncomeMap.set(key, value);\r\n      });\r\n    }\r\n\r\n    // Create aligned data arrays\r\n    const incomeData = monthlyData.map((item) => parseFloat(item.totalIncome) / 1000 || 0);\r\n    const cogsData = monthlyData.map((item) => parseFloat(item.totalCOGS) / 1000 || 0);\r\n    const expenseData = monthlyData.map((item) => parseFloat(item.totalExpenses) / 1000 || 0);\r\n\r\n    const grossProfitMarginData = monthlyData.map((item) => {\r\n      const key = `${item.year}-${item.month}`;\r\n      return grossProfitMarginMap.get(key) || 0;\r\n    });\r\n\r\n\r\n    const netProfitMarginRaw = monthlyData.map((item) => {\r\n      const key = `${item.year}-${item.month}`;\r\n      return netProfitMarginMap.get(key) || 0;\r\n    });\r\n\r\n    const netIncomeData = monthlyData.map((item) => {\r\n      const key = `${item.year}-${item.month}`;\r\n      return netIncomeMap.get(key) || 0;\r\n    });\r\n\r\n    // 1. Stacked Column Chart (incomeSummary)\r\n    const stackedColumnOptions = {\r\n      series: [\r\n        { name: \"Income\", type: \"line\", data: incomeData },\r\n        { name: \"Expense\", type: \"column\", data: expenseData },\r\n        { name: \"Cost of Goods Sold\", type: \"column\", data: cogsData },\r\n      ],\r\n      chart: {\r\n        height: 450,\r\n        type: \"bar\",\r\n        stacked: true,\r\n        toolbar: { show: false },\r\n        background: \"transparent\",\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        enabledOnSeries: [0, 1, 2], // Show labels on all three series: Income, Expense, and COGS\r\n        formatter: function (val, opts) {\r\n          if (val === null || val === undefined || isNaN(val)) return \"\";\r\n\r\n          const absVal = Math.abs(val);\r\n\r\n          if (absVal >= 1000) {\r\n            return '$' + (val / 1000).toFixed(1) + 'm';\r\n          } else if (absVal >= 1) {\r\n            return '$' + val.toFixed(2) + 'k';\r\n          } else {\r\n            return '$' + (val * 1000).toFixed(0);\r\n          }\r\n        },\r\n        style: {\r\n          fontSize: \"14px\",\r\n          colors: [\"#20b2aa\", \"#333\", \"#333\"], // Different colors for each series\r\n          fontWeight: \"500\",\r\n        },\r\n        offsetY: -10,\r\n        background: {\r\n          enabled: false,\r\n        },\r\n        dropShadow: {\r\n          enabled: false,\r\n        },\r\n      },\r\n      stroke: {\r\n        width: [2, 0, 0],\r\n        curve: \"smooth\",\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          columnWidth: \"60%\",\r\n          dataLabels: {\r\n            total: {\r\n              enabled: false, // Disable total labels\r\n              offsetY: -20,\r\n              style: {\r\n                fontSize: \"14px\",\r\n                fontWeight: \"500\",\r\n                color: \"#333\",\r\n              },\r\n              formatter: function (val) {\r\n                if (val === null || val === undefined || isNaN(val)) return \"$0\";\r\n\r\n                const absVal = Math.abs(val);\r\n\r\n                if (absVal >= 1000) {\r\n                  return '$' + (val / 1000).toFixed(1) + 'm';\r\n                } else if (absVal >= 1) {\r\n                  return '$' + val.toFixed(1) + 'k';\r\n                } else {\r\n                  return '$' + (val * 1000).toFixed(0);\r\n                }\r\n              },\r\n            },\r\n          },\r\n        },\r\n      },\r\n      fill: {\r\n        opacity: [1, 1, 1], // Make all series fully opaque\r\n      },\r\n      labels: categories,\r\n      markers: {\r\n        size: [5, 0, 0],\r\n        fontSize: \"14px\",\r\n        strokeColors: \"#fff\",\r\n        strokeWidth: 2,\r\n        fillOpacity: 1,\r\n        hover: {\r\n          size: 7,\r\n        },\r\n      },\r\n      xaxis: {\r\n        labels: {\r\n          style: {\r\n            colors: \"#666\",\r\n            fontSize: \"14px\",\r\n          },\r\n          offsetY: 15, // Push month labels down by 15px\r\n        },\r\n        axisBorder: {\r\n          show: false,\r\n        },\r\n        axisTicks: {\r\n          show: false,\r\n        },\r\n      },\r\n      yaxis: {\r\n        show: false,\r\n        // More conservative scaling to ensure small COGS values are visible\r\n        min: 0,\r\n        max: function () {\r\n          // Calculate max considering stacked values\r\n          const maxStacked = Math.max(...cogsData.map((cogs, i) => cogs + expenseData[i]));\r\n          const maxIncome = Math.max(...incomeData);\r\n          return Math.max(maxStacked, maxIncome) * 1.2;\r\n        },\r\n      },\r\n      colors: [\"#20b2aa\", \"#ff8a80\", \"#53579f\"], // Colors for Income, Expense, COGS\r\n      legend: {\r\n        position: \"bottom\",\r\n        horizontalAlign: \"center\",\r\n        fontSize: \"14px\",\r\n        fontWeight: \"400\",\r\n        markers: {\r\n          width: 12,\r\n          height: 12,\r\n          radius: 6, // Circular markers\r\n        },\r\n        labels: {\r\n          colors: \"#333\",\r\n          useSeriesColors: false,\r\n        },\r\n        itemMargin: {\r\n          horizontal: 15,\r\n          vertical: 4,\r\n        },\r\n        offsetY: 10,\r\n        onItemClick: {\r\n          toggleDataSeries: true, // Enable clicking to hide/show series\r\n        },\r\n        onItemHover: {\r\n          highlightDataSeries: true, // Enable hover highlighting\r\n        },\r\n      },\r\n      tooltip: {\r\n        shared: true,\r\n        intersect: false,\r\n        // Custom tooltip to clearly show all values\r\n        custom: function ({ series, seriesIndex, dataPointIndex, w }) {\r\n          const income = (series[0][dataPointIndex] * 1000).toFixed(0);\r\n          const expense = (series[1][dataPointIndex] * 1000).toFixed(0);\r\n          const cogs = (series[2][dataPointIndex] * 1000).toFixed(0);\r\n          const category = w.globals.labels[dataPointIndex];\r\n\r\n          return `\r\n        <div style=\"padding: 12px; background: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); font-size: 14px;\">\r\n          <div style=\"font-weight: 600; margin-bottom: 8px; color: #333;\">${category}</div>\r\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\r\n            <div style=\"width: 12px; height: 12px; background: #20b2aa; border-radius: 50%; margin-right: 8px;\"></div>\r\n            <span style=\"color: #333;\">Income: <strong>$${income}</strong></span>\r\n          </div>\r\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\r\n            <div style=\"width: 12px; height: 12px; background: #ff8a80; border-radius: 50%; margin-right: 8px;\"></div>\r\n            <span style=\"color: #333;\">Expense: <strong>$${expense}</strong></span>\r\n          </div>\r\n          <div style=\"display: flex; align-items: center; margin-bottom: 4px; ${cogs == 0 ? 'opacity: 0.6;' : ''}\">\r\n            <div style=\"width: 12px; height: 12px; background: #4361ee; border-radius: 50%; margin-right: 8px;\"></div>\r\n            <span style=\"color: #333;\">COGS: <strong>$${cogs}</strong> ${cogs == 0 ? '(No data)' : ''}</span>\r\n          </div>\r\n          <div style=\"margin-top: 8px; padding-top: 8px; border-top: 1px solid #eee; font-size: 12px; color: #666;\">\r\n            Total Costs: <strong>$${(parseFloat(expense) + parseFloat(cogs)).toFixed(0)}</strong>\r\n          </div>\r\n        </div>\r\n      `;\r\n        },\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 30, // Increased bottom padding from 0 to 30\r\n        },\r\n      },\r\n      // Add annotations to highlight if COGS data exists\r\n      annotations: {\r\n        yaxis: cogsData.some(val => val > 0) ? [] : [{\r\n          y: 0,\r\n          borderColor: '#FF4560',\r\n          label: {\r\n            borderColor: '#FF4560',\r\n            style: {\r\n              color: '#fff',\r\n              background: '#FF4560',\r\n            },\r\n          }\r\n        }]\r\n      }\r\n    };\r\n\r\n    // 2. Net Income Chart\r\n    const netIncomeOptions = {\r\n      series: [{\r\n        name: 'Net Income',\r\n        data: netIncomeData\r\n      }],\r\n      chart: {\r\n        type: 'line',\r\n        height: 300,\r\n        toolbar: { show: false },\r\n        background: 'transparent',\r\n        zoom: {\r\n          enabled: false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          if (val >= 1) {\r\n            return '$' + val.toFixed(2) + 'k';\r\n          } else if (val >= 0.1) {\r\n            return '$' + val.toFixed(2) + 'k';\r\n          } else if (val <= -0.1) {\r\n            return '-$' + Math.abs(val).toFixed(2) + 'k';\r\n          } else {\r\n            return '$' + val.toFixed(0) + 'k';\r\n          }\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -15,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'straight',\r\n        width: 3\r\n      },\r\n      fill: {\r\n        type: 'solid'\r\n      },\r\n      markers: {\r\n        size: 5,\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        colors: netIncomeData.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\r\n        hover: {\r\n          size: 7\r\n        },\r\n        discrete: netIncomeData.map((val, index) => ({\r\n          seriesIndex: 0,\r\n          dataPointIndex: index,\r\n          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\r\n          strokeColor: '#fff',\r\n          size: 5\r\n        }))\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px',\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: ['#1E7C8C'], // Default color for positive values\r\n      plotOptions: {\r\n        line: {\r\n          colors: {\r\n            threshold: 0,\r\n            colorAboveThreshold: '#1E7C8C', // Teal for positive values\r\n            colorBelowThreshold: '#d70015'   // Red for negative values\r\n          }\r\n        }\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            if (val >= 1) {\r\n              return '$' + val.toFixed(2) + ' k';\r\n            } else if (val >= 0.1) {\r\n              return '$' + val.toFixed(2) + ' k';\r\n            } else if (val <= -0.1) {\r\n              return '-$' + Math.abs(val).toFixed(2) + ' k';\r\n            } else {\r\n              return '$' + val.toFixed(0) + ' k';\r\n            }\r\n          }\r\n        }\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 25,\r\n          bottom: 0\r\n        }\r\n      },\r\n      annotations: {\r\n        yaxis: [{\r\n          y: 0,\r\n          borderColor: '#666',\r\n          borderWidth: 1,\r\n          strokeDashArray: 0,\r\n          opacity: 0.8\r\n        }]\r\n      }\r\n    };\r\n\r\n    // 3. Gross Profit Margin Chart\r\n    const grossProfitOptions = {\r\n      series: [{ name: \"Gross Profit Margin\", data: grossProfitMarginData }],\r\n      chart: {\r\n        type: \"bar\",\r\n        height: 350,\r\n        toolbar: { show: false },\r\n        background: \"transparent\",\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          horizontal: false,\r\n          columnWidth: \"55%\",\r\n          endingShape: \"rounded\",\r\n          dataLabels: { position: \"top\" },\r\n        },\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        position: 'top',\r\n        formatter: function (val) {\r\n          if (val >= 1) {\r\n            return val.toFixed(2) + '%';\r\n          } else if (val >= 0.1) {\r\n            return val.toFixed(2) + '%';\r\n          } else if (val <= -0.1) {\r\n            return '-' + Math.abs(val).toFixed(2) + '%';\r\n          } else {\r\n            return val.toFixed(0) + '%';\r\n          }\r\n        },\r\n        offsetY: -20,\r\n        style: { fontSize: \"14px\", colors: [\"#333\"], fontWeight: \"500\" },\r\n      },\r\n      stroke: { show: true, width: 2, colors: [\"transparent\"] },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: { style: { colors: \"#666\", fontSize: \"14px\" } },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false },\r\n      },\r\n      yaxis: {\r\n        show: false,\r\n        min: Math.min(...grossProfitMarginData) < 0 ? Math.min(...grossProfitMarginData) * 1.1 : 0,\r\n        max: Math.max(...grossProfitMarginData) * 1.2,\r\n      },\r\n      fill: { opacity: 1 },\r\n      colors: [\"#4a4a9a\"],\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            if (val === null || val === undefined || isNaN(val)) return \"N/A\";\r\n            return val.toFixed(2) + \"%\";\r\n          },\r\n        },\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: { left: 25, right: 25, top: 20, bottom: 0 },\r\n      },\r\n    };\r\n\r\n    // 4. Net Profit Margin Chart\r\n    const netProfitMarginOptions = {\r\n      series: [{\r\n        name: 'Net Profit Margin',\r\n        data: netProfitMarginRaw\r\n      }],\r\n      chart: {\r\n        type: 'line',\r\n        height: 300,\r\n        toolbar: { show: false },\r\n        background: 'transparent',\r\n        zoom: {\r\n          enabled: false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          if (val >= 1) {\r\n            return val.toFixed(2) + '%';\r\n          } else if (val >= 0.1) {\r\n            return val.toFixed(2) + '%';\r\n          } else if (val <= -0.1) {\r\n            return '-' + Math.abs(val).toFixed(2) + '%';\r\n          } else {\r\n            return val.toFixed(0) + '%';\r\n          }\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -15,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'straight',\r\n        width: 3\r\n      },\r\n      fill: {\r\n        type: 'solid'\r\n      },\r\n      markers: {\r\n        size: 5,\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        colors: netProfitMarginRaw.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\r\n        hover: {\r\n          size: 7\r\n        },\r\n        discrete: netProfitMarginRaw.map((val, index) => ({\r\n          seriesIndex: 0,\r\n          dataPointIndex: index,\r\n          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\r\n          strokeColor: '#fff',\r\n          size: 5\r\n        }))\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px'\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: ['#1E7C8C'], // Default line color\r\n      plotOptions: {\r\n        line: {\r\n          colors: {\r\n            threshold: 0,\r\n            colorAboveThreshold: '#1E7C8C',\r\n            colorBelowThreshold: '#d70015',\r\n          },\r\n        }\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            if (val >= 1) {\r\n              return val.toFixed(2) + '%';\r\n            } else if (val >= 0.1) {\r\n              return val.toFixed(2) + '%';\r\n            } else if (val <= -0.1) {\r\n              return '-' + Math.abs(val).toFixed(2) + '%';\r\n            } else {\r\n              return val.toFixed(0) + '%';\r\n            }\r\n          }\r\n        }\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 25,\r\n          bottom: 0\r\n        }\r\n      },\r\n      annotations: {\r\n        yaxis: [{\r\n          y: 0,\r\n          borderColor: '#666',\r\n          borderWidth: 1,\r\n          strokeDashArray: 0,\r\n          opacity: 0.8\r\n        }]\r\n      }\r\n    };\r\n\r\n    // Clear existing charts\r\n    [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach((ref) => {\r\n      if (ref.current) {\r\n        ref.current.innerHTML = \"\";\r\n      }\r\n    });\r\n\r\n\r\n\r\n    // Get enabled charts and assign chart options\r\n    const enabledCharts = getEnabledCharts();\r\n\r\n    // Assign chart options to enabled charts\r\n    enabledCharts.forEach(chart => {\r\n      switch (chart.key) {\r\n        case 'incomeSummary':\r\n          chart.options = stackedColumnOptions;\r\n          chart.name = 'Stacked Column';\r\n          break;\r\n        case 'netIncome':\r\n          chart.options = netIncomeOptions;\r\n          chart.name = 'Net Income';\r\n          break;\r\n        case 'grossProfitMargin':\r\n          chart.options = grossProfitOptions;\r\n          chart.name = 'Gross Profit Margin';\r\n          break;\r\n        case 'netProfitMargin':\r\n          chart.options = netProfitMarginOptions;\r\n          chart.name = 'Net Profit Margin';\r\n          break;\r\n      }\r\n    });\r\n\r\n    // Clear existing charts before rendering new ones\r\n    const clearAndRenderChart = (ref, options, chartName) => {\r\n      if (ref.current) {\r\n        // Clear any existing chart\r\n        ref.current.innerHTML = '';\r\n\r\n        // Wait a tick before rendering to ensure DOM is cleared\r\n        setTimeout(() => {\r\n          if (ref.current) {\r\n            try {\r\n              const chart = new ApexCharts(ref.current, options);\r\n              chart.render();\r\n\r\n              // Store chart instances globally for export\r\n              if (chartName === \"Stacked Column\") {\r\n                window.stackedColumnChart = chart;\r\n              } else if (chartName === \"Net Income\") {\r\n                window.netIncomeChart = chart;\r\n              } else if (chartName === \"Gross Profit Margin\") {\r\n                window.grossProfitChart = chart;\r\n              } else if (chartName === \"Net Profit Margin\") {\r\n                window.netProfitMarginChart = chart;\r\n              }\r\n            } catch (error) {\r\n              console.error(`FiscalYear - Error rendering ${chartName} chart:`, error);\r\n              // Show error message in chart container\r\n              ref.current.innerHTML = `<div class=\"flex items-center justify-center h-48 text-gray-500\">Error loading ${chartName} chart</div>`;\r\n            }\r\n          }\r\n        }, 10);\r\n      }\r\n    };\r\n\r\n    // Helper function to check if data array has meaningful values (not all zeros)\r\n    const hasMeaningfulData = (dataArray) => {\r\n      return dataArray && dataArray.length > 0 && dataArray.some(val => parseFloat(val) !== 0);\r\n    };\r\n\r\n    // Render charts with fallback for empty/zero data\r\n    enabledCharts.forEach(({ ref, options, name, key }) => {\r\n      if (ref.current) {\r\n        let hasData = false;\r\n\r\n        // Check if chart has meaningful data based on chart type\r\n        if (key === 'incomeSummary') {\r\n          const monthlyData = fiscalData?.monthlyPerformanceBreakDown || [];\r\n          hasData = monthlyData.some(item => {\r\n            const income = parseFloat(item.totalIncome || 0);\r\n            const cogs = parseFloat(item.totalCOGS || 0);\r\n            const expenses = parseFloat(item.totalExpenses || 0);\r\n            return income !== 0 || cogs !== 0 || expenses !== 0;\r\n          });\r\n        } else if (key === 'netIncome') {\r\n          const netIncomeData = fiscalData?.netIncomeLoss?.map(item => parseFloat(item.netIncomeLoss || 0)) || [];\r\n          hasData = hasMeaningfulData(netIncomeData);\r\n        } else if (key === 'grossProfitMargin') {\r\n          const grossProfitData = fiscalData?.monthlyGrossProfitMargin?.map(item => parseFloat(item.Gross_Profit_Margin || 0)) || [];\r\n          hasData = hasMeaningfulData(grossProfitData);\r\n        } else if (key === 'netProfitMargin') {\r\n          const netProfitData = fiscalData?.nerProfitMargin?.map(item => parseFloat(item.nerProfitMargin || 0)) || [];\r\n          hasData = hasMeaningfulData(netProfitData);\r\n        }\r\n\r\n        if (hasData) {\r\n          clearAndRenderChart(ref, options, name);\r\n        } else {\r\n          ref.current.innerHTML = `<div class=\"flex items-center justify-center h-64 text-gray-500\">No meaningful ${name.toLowerCase()} data available</div>`;\r\n        }\r\n      }\r\n    });\r\n\r\n    // Clear all chart containers that are not being used\r\n    [stackedColumnRef, netIncomeRef, grossProfitRef, netProfitMarginRef].forEach((ref) => {\r\n      if (ref.current && !enabledCharts.some(chart => chart.ref === ref)) {\r\n        ref.current.innerHTML = \"\";\r\n      }\r\n    });\r\n  };\r\n\r\n\r\n  const formatHeaderPeriod = (startYear, startMonth) => {\r\n    const monthNames = [\r\n      \"January\", \"February\", \"March\", \"April\", \"May\", \"June\",\r\n      \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"\r\n    ];\r\n\r\n    if (!startYear || !startMonth) {\r\n      return \" \"; // fallback\r\n    }\r\n\r\n    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index\r\n\r\n    return `${startMonthName} ${startYear}`;\r\n  };\r\n\r\n  const formatHeaderStyle = () => {\r\n    const style = { ...headerTextStyle };\r\n\r\n    if (style.fontSize) {\r\n      const fontSize = parseInt(style.fontSize);\r\n      style.fontSize = `${fontSize / 2}px`;\r\n    }\r\n    return style;\r\n  };\r\n\r\n  const formatCompanyName = (companyName) => {\r\n    if (!companyName) return '';\r\n\r\n    if (companyName.length > 15) {\r\n      return companyName.substring(0, 15) + '...';\r\n    }\r\n\r\n    return companyName;\r\n  };\r\n\r\n  // Calculate YTD totals from monthly data\r\n  const calculateYTDTotals = () => {\r\n    if (!fiscalData.monthlyPerformanceBreakDown) return {};\r\n\r\n    return fiscalData.monthlyPerformanceBreakDown.reduce(\r\n      (totals, month) => {\r\n        totals.totalIncome += parseFloat(month.totalIncome || 0);\r\n        totals.totalCOGS += parseFloat(month.totalCOGS || 0);\r\n        totals.totalExpenses += parseFloat(month.totalExpenses || 0);\r\n        return totals;\r\n      },\r\n      { totalIncome: 0, totalCOGS: 0, totalExpenses: 0 }\r\n    );\r\n  };\r\n\r\n  // Function to determine which charts should be rendered and their order\r\n  const getEnabledCharts = () => {\r\n    const allCharts = [\r\n      {\r\n        key: 'incomeSummary',\r\n        title: 'Monthly Performance Breakdown',\r\n        ref: stackedColumnRef,\r\n        options: null, // Will be set in initializeCharts\r\n        hasData: () => {\r\n          if (!fiscalData?.monthlyPerformanceBreakDown) return false;\r\n          const monthlyData = fiscalData.monthlyPerformanceBreakDown;\r\n          return monthlyData.some(item => {\r\n            const income = parseFloat(item.totalIncome || 0);\r\n            const cogs = parseFloat(item.totalCOGS || 0);\r\n            const expenses = parseFloat(item.totalExpenses || 0);\r\n            return income > 0 || cogs > 0 || expenses > 0;\r\n          });\r\n        }\r\n      },\r\n      {\r\n        key: 'netIncome',\r\n        title: 'Net Income/(Loss)',\r\n        ref: netIncomeRef,\r\n        options: null,\r\n        hasData: () => {\r\n          if (!fiscalData?.netIncomeLoss) return false;\r\n          return fiscalData.netIncomeLoss.some(item => parseFloat(item.netIncomeLoss || 0) !== 0);\r\n        }\r\n      },\r\n      {\r\n        key: 'grossProfitMargin',\r\n        title: 'Gross Profit Margin',\r\n        ref: grossProfitRef,\r\n        options: null,\r\n        hasData: () => {\r\n          if (!fiscalData?.monthlyGrossProfitMargin) return false;\r\n          return fiscalData.monthlyGrossProfitMargin.some(item => parseFloat(item.Gross_Profit_Margin || 0) !== 0);\r\n        },\r\n        hasDescription: true,\r\n        description: {\r\n          title: 'Gross Profit Margin',\r\n          content: 'Is a share of Gross Profit in Total Income or the profit left for covering operating and other expenses. A good Gross Profit Margin is high enough to cover overhead and leave a reasonable Net Profit.'\r\n        }\r\n      },\r\n      {\r\n        key: 'netProfitMargin',\r\n        title: 'Net Profit Margin',\r\n        ref: netProfitMarginRef,\r\n        options: null,\r\n        hasData: () => {\r\n          if (!fiscalData?.nerProfitMargin) return false;\r\n          return fiscalData.nerProfitMargin.some(item => parseFloat(item.nerProfitMargin || 0) !== 0);\r\n        },\r\n        hasDescription: true,\r\n        description: {\r\n          title: 'Net Profit Margin',\r\n          content: 'Shows the profit earned per dollar of income. A 10% Net Profit Margin is considered an excellent ratio. If your company has a low Net Profit Margin you are making very little profit after all costs. That implies the revenue is getting eaten up by expenses. It also increases the risk your firm will be unable to meet obligations. With a low margin, a sudden dip in sales over the next month or year could turn your company unprofitable. A high margin indicates your company has solid competitive advantages.'\r\n        }\r\n      }\r\n    ];\r\n\r\n    // Filter charts based on settings and data availability\r\n    return allCharts.filter(chart =>\r\n      shouldDisplayChart(chart.key)\r\n    );\r\n  };\r\n\r\n  const ytdTotals = calculateYTDTotals();\r\n  // Fix floating point precision for net profit calculation\r\n  const netProfit =\r\n    Math.round(\r\n      (ytdTotals.totalIncome - ytdTotals.totalCOGS - ytdTotals.totalExpenses) *\r\n      100\r\n    ) / 100;\r\n\r\n  // Get enabled charts for dynamic layout\r\n  const enabledCharts = getEnabledCharts();\r\n\r\n  // Split charts between upper and lower divs\r\n  // Upper div can hold up to 2 charts, lower div gets the rest\r\n  const upperDivCharts = enabledCharts.slice(0, 2);\r\n  const lowerDivCharts = enabledCharts.slice(2);\r\n\r\n  // Helper function to render a chart component\r\n  const renderChart = (chart) => (\r\n    <div key={chart.key} className=\"bg-white p-6 border-b-4 border-blue-900 mb-4\">\r\n      <div\r\n        className=\"text-2xl font-semibold text-teal-600 mb-3\"\r\n        style={subHeadingTextStyle}\r\n      >\r\n        {chart.title}\r\n      </div>\r\n      <div ref={chart.ref}></div>\r\n      {chart.hasDescription && (\r\n        <div className=\"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\">\r\n          <div\r\n            className=\"text-teal-600 text-2xl\"\r\n            style={{ ...subHeadingTextStyle, fontWeight: \"lighter\" }}\r\n          >\r\n            {chart.description.title}\r\n          </div>\r\n          <div style={contentTextStyle}>\r\n            {chart.description.content}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <div className=\"p-5\">\r\n      <div className=\"max-w-6xl h-[400mm] mx-auto bg-white flex flex-col gap-3 p-10 mb-4\">\r\n        {/* Header Section */}\r\n        <div className=\"component-header flex items-center justify-between gap-4  border-b-4 border-blue-900 pb-2\">\r\n          <h1\r\n            className=\"text-4xl font-bold text-gray-800 m-0\"\r\n            style={headerTextStyle}\r\n          >\r\n            Current Fiscal Year\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={formatHeaderStyle()}>\r\n            {formatHeaderPeriod(fiscalData?.FYEndYear, fiscalData?.FYStartMonth)} | {formatCompanyName(fiscalData?.companyName)}\r\n          </p>\r\n        </div>\r\n\r\n        {/* YTD Fiscal Metrics Grid */}\r\n        <div className=\"metrics-flex grid grid-cols-4 gap-5 pb-6 pt-3 border-b-4 border-blue-900\">\r\n          <div className=\"p-4 text-center\">\r\n            <div className=\"text-xl mb-1\"\r\n              style={{ ...contentTextStyle, fontSize: '20px', color: \"#4b5562\" }}\r\n            >YTD Total Income</div>\r\n            <div className=\"text-4xl font-bold text-gray-600 m-0\">\r\n              {formatNumber(ytdTotals.totalIncome)}\r\n            </div>\r\n          </div>\r\n          <div\r\n            className=\"p-4 text-center\"\r\n            style={{ backgroundColor: \"#d2e9ea\" }}\r\n          >\r\n            <div className=\"text-xl mb-1\"\r\n              style={{ ...contentTextStyle, fontSize: '20px', color: \"#4b5562\" }}\r\n            >YTD Cost of Goods Sold</div>\r\n            <div className=\"text-4xl font-bold text-gray-600 m-0\">\r\n              {formatNumber(ytdTotals.totalCOGS)}\r\n            </div>\r\n          </div>\r\n          <div className=\"p-4 text-center\">\r\n            <div className=\"text-xl mb-1\"\r\n              style={{ ...contentTextStyle, fontSize: '20px', color: \"#4b5562\" }}\r\n            >YTD Total Expense</div>\r\n            <div className=\"text-4xl font-bold text-gray-600 m-0\">\r\n              {formatNumber(ytdTotals.totalExpenses)}\r\n            </div>\r\n          </div>\r\n          <div\r\n            className=\"p-4 text-center\"\r\n            style={{ backgroundColor: \"#d2e9ea\" }}\r\n          >\r\n            <div className=\"text-xl mb-1\"\r\n              style={{ ...contentTextStyle, fontSize: '20px', color: \"#4b5562\" }}\r\n            >YTD Net Profit</div>\r\n            <div className=\"text-4xl font-bold text-gray-600 m-0\">\r\n              {formatNumber(netProfit)}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Dynamically render charts for upper div */}\r\n        {upperDivCharts.map(chart => renderChart(chart))}\r\n\r\n      </div>\r\n\r\n      {/* Only render lower div if there are charts to display */}\r\n      {lowerDivCharts.length > 0 && (\r\n        <div className=\"max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-4 p-10\">\r\n          <div className=\"component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pt-2 pb-2\">\r\n            <h1\r\n              className=\"text-4xl font-bold text-gray-800 m-0\"\r\n              style={headerTextStyle}\r\n            >\r\n              Current Fiscal Year\r\n            </h1>\r\n            <p className=\"text-lg text-gray-600 m-0\" style={formatHeaderStyle()}>\r\n              {formatHeaderPeriod(fiscalData?.FYEndYear, fiscalData?.FYStartMonth)} | {formatCompanyName(fiscalData?.companyName)}\r\n            </p>\r\n          </div>\r\n\r\n          {/* Dynamically render charts for lower div */}\r\n          {lowerDivCharts.map(chart => renderChart(chart))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FiscalYearDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAOC,UAAU,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,mBAAmB,GAAGA,CAAC;EAC3BC,eAAe,GAAG,CAAC,CAAC;EACpBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,UAAU,GAAG,IAAI;EACjBC,eAAe,GAAG,IAAI,CAAE;AAC1B,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,gBAAgB,GAAGX,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMY,YAAY,GAAGZ,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMa,cAAc,GAAGb,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMc,kBAAkB,GAAGd,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAMe,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACP,UAAU,EAAE;MACf,OAAO,KAAK;IACd;;IAEA;IACA,MAAMQ,cAAc,GAAGR,UAAU,CAACS,2BAA2B,IAC3DC,KAAK,CAACC,OAAO,CAACX,UAAU,CAACS,2BAA2B,CAAC,IACrDT,UAAU,CAACS,2BAA2B,CAACG,MAAM,GAAG,CAAC;IAEnD,MAAMC,oBAAoB,GAAGb,UAAU,CAACc,wBAAwB,IAC9DJ,KAAK,CAACC,OAAO,CAACX,UAAU,CAACc,wBAAwB,CAAC,IAClDd,UAAU,CAACc,wBAAwB,CAACF,MAAM,GAAG,CAAC;IAEhD,MAAMG,kBAAkB,GAAGf,UAAU,CAACgB,eAAe,IACnDN,KAAK,CAACC,OAAO,CAACX,UAAU,CAACgB,eAAe,CAAC,IACzChB,UAAU,CAACgB,eAAe,CAACJ,MAAM,GAAG,CAAC;IAEvC,MAAMK,gBAAgB,GAAGjB,UAAU,CAACkB,aAAa,IAC/CR,KAAK,CAACC,OAAO,CAACX,UAAU,CAACkB,aAAa,CAAC,IACvClB,UAAU,CAACkB,aAAa,CAACN,MAAM,GAAG,CAAC;IACrC;IACA,OAAOJ,cAAc;EACvB,CAAC;;EAED;EACA;EACA;EACA;EACA;;EAEA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;;EAEA;EACA,MAAMW,kBAAkB,GAAIC,QAAQ,IAAK;IACvC,IAAI,EAACnB,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEoB,aAAa,GAAE,OAAO,IAAI,CAAC,CAAC;IAClD,OAAOpB,eAAe,CAACoB,aAAa,CAACD,QAAQ,CAAC,KAAK,IAAI;EACzD,CAAC;EAED7B,SAAS,CAAC,MAAM;IACd,IAAIgB,YAAY,CAAC,CAAC,EAAE;MAClB;MACA,CAACJ,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,kBAAkB,CAAC,CAACgB,OAAO,CAAEC,GAAG,IAAK;QACpF,IAAIA,GAAG,CAACC,OAAO,EAAE;UACfD,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,EAAE;QAC5B;MACF,CAAC,CAAC;MACF;MACAC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAC1B,UAAU,EAAEC,eAAe,CAAC,CAAC,CAAC,CAAC;;EAEnC,MAAM0B,eAAe,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;IACvC,MAAMC,UAAU,GAAG,CACjB,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;IACD,OAAO,GAAGA,UAAU,CAACD,KAAK,GAAG,CAAC,CAAC,IAAIE,MAAM,CAACH,IAAI,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7D,CAAC;EAED,SAASC,YAAYA,CAACC,GAAG,EAAE;IACzB;IACA,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;IAC9C,MAAMI,UAAU,GAAGH,UAAU,GAAG,CAAC;IACjC,MAAMI,MAAM,GAAGH,IAAI,CAACI,GAAG,CAACL,UAAU,CAAC;;IAEnC;IACA,IAAII,MAAM,GAAG,IAAI,EAAE;MACjB,OAAO,CAACD,UAAU,GAAG,GAAG,GAAG,EAAE,KAAKC,MAAM,GAAG,CAAC,KAAK,CAAC,GAAGA,MAAM,CAACE,QAAQ,CAAC,CAAC,GAAGF,MAAM,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7F;IAEA,MAAMC,QAAQ,GAAG,CACf;MAAEC,KAAK,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAI,CAAC,EAC5B;MAAED,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAC,EAC3B;MAAED,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAC,EAC3B;MAAED,KAAK,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAC,CAC5B;IAED,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,CAAC/B,MAAM,EAAEkC,CAAC,EAAE,EAAE;MACxC,IAAIP,MAAM,IAAII,QAAQ,CAACG,CAAC,CAAC,CAACF,KAAK,EAAE;QAC/B,MAAMG,SAAS,GAAG,CAACR,MAAM,GAAGI,QAAQ,CAACG,CAAC,CAAC,CAACF,KAAK,EAAEF,OAAO,CAAC,CAAC,CAAC;QACzD,MAAMM,cAAc,GAAGD,SAAS,CAACE,QAAQ,CAAC,IAAI,CAAC,GAC3CF,SAAS,CAACf,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GACtBe,SAAS;QACb,OAAO,CAACT,UAAU,GAAG,GAAG,GAAG,EAAE,IAAIU,cAAc,GAAGL,QAAQ,CAACG,CAAC,CAAC,CAACD,MAAM;MACtE;IACF;IAEA,OAAO,CAACP,UAAU,GAAG,GAAG,GAAG,EAAE,IAAIH,UAAU,CAACM,QAAQ,CAAC,CAAC;EACxD;EAEA,MAAMf,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAAC1B,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAES,2BAA2B,GAAE;IAE9C,MAAMyC,WAAW,GAAGlD,UAAU,CAACS,2BAA2B;;IAE1D;IACA,MAAM0C,UAAU,GAAGD,WAAW,CAACE,GAAG,CAAEC,IAAI,IACtC1B,eAAe,CAAC0B,IAAI,CAACzB,IAAI,EAAEyB,IAAI,CAACxB,KAAK,CACvC,CAAC;;IAED;IACA,MAAMyB,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACpC,IAAIvD,UAAU,CAACgB,eAAe,IAAIN,KAAK,CAACC,OAAO,CAACX,UAAU,CAACgB,eAAe,CAAC,EAAE;MAC3EhB,UAAU,CAACgB,eAAe,CAACM,OAAO,CAAC+B,IAAI,IAAI;QACzC,MAAMG,GAAG,GAAG,GAAGH,IAAI,CAACzB,IAAI,IAAIyB,IAAI,CAACxB,KAAK,EAAE;QACxC,MAAMe,KAAK,GAAGa,UAAU,CAACJ,IAAI,CAACrC,eAAe,CAAC,IAAI,CAAC;QACnDsC,kBAAkB,CAACI,GAAG,CAACF,GAAG,EAAEZ,KAAK,CAAC;MACpC,CAAC,CAAC;IACJ;IAEA,MAAMe,oBAAoB,GAAG,IAAIJ,GAAG,CAAC,CAAC;IACtC,IAAIvD,UAAU,CAACc,wBAAwB,IAAIJ,KAAK,CAACC,OAAO,CAACX,UAAU,CAACc,wBAAwB,CAAC,EAAE;MAC7Fd,UAAU,CAACc,wBAAwB,CAACQ,OAAO,CAAC+B,IAAI,IAAI;QAClD,MAAMG,GAAG,GAAG,GAAGH,IAAI,CAACzB,IAAI,IAAIyB,IAAI,CAACxB,KAAK,EAAE;QACxC,MAAMe,KAAK,GAAGa,UAAU,CAACJ,IAAI,CAACO,mBAAmB,CAAC;QAClD,MAAMC,UAAU,GAAGjB,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAGA,KAAK,GAAG,GAAG;QAClDe,oBAAoB,CAACD,GAAG,CAACF,GAAG,EAAEK,UAAU,IAAI,CAAC,CAAC;MAChD,CAAC,CAAC;IACJ;IAEA,MAAMC,YAAY,GAAG,IAAIP,GAAG,CAAC,CAAC;IAC9B,IAAIvD,UAAU,CAACkB,aAAa,IAAIR,KAAK,CAACC,OAAO,CAACX,UAAU,CAACkB,aAAa,CAAC,EAAE;MACvElB,UAAU,CAACkB,aAAa,CAACI,OAAO,CAAC+B,IAAI,IAAI;QACvC,MAAMG,GAAG,GAAG,GAAGH,IAAI,CAACzB,IAAI,IAAIyB,IAAI,CAACxB,KAAK,EAAE;QACxC,MAAMe,KAAK,GAAGa,UAAU,CAACJ,IAAI,CAACnC,aAAa,CAAC,GAAG,IAAI,IAAI,CAAC;QACxD4C,YAAY,CAACJ,GAAG,CAACF,GAAG,EAAEZ,KAAK,CAAC;MAC9B,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMmB,UAAU,GAAGb,WAAW,CAACE,GAAG,CAAEC,IAAI,IAAKI,UAAU,CAACJ,IAAI,CAACW,WAAW,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;IACtF,MAAMC,QAAQ,GAAGf,WAAW,CAACE,GAAG,CAAEC,IAAI,IAAKI,UAAU,CAACJ,IAAI,CAACa,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;IAClF,MAAMC,WAAW,GAAGjB,WAAW,CAACE,GAAG,CAAEC,IAAI,IAAKI,UAAU,CAACJ,IAAI,CAACe,aAAa,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC;IAEzF,MAAMC,qBAAqB,GAAGnB,WAAW,CAACE,GAAG,CAAEC,IAAI,IAAK;MACtD,MAAMG,GAAG,GAAG,GAAGH,IAAI,CAACzB,IAAI,IAAIyB,IAAI,CAACxB,KAAK,EAAE;MACxC,OAAO8B,oBAAoB,CAACW,GAAG,CAACd,GAAG,CAAC,IAAI,CAAC;IAC3C,CAAC,CAAC;IAGF,MAAMe,kBAAkB,GAAGrB,WAAW,CAACE,GAAG,CAAEC,IAAI,IAAK;MACnD,MAAMG,GAAG,GAAG,GAAGH,IAAI,CAACzB,IAAI,IAAIyB,IAAI,CAACxB,KAAK,EAAE;MACxC,OAAOyB,kBAAkB,CAACgB,GAAG,CAACd,GAAG,CAAC,IAAI,CAAC;IACzC,CAAC,CAAC;IAEF,MAAMgB,aAAa,GAAGtB,WAAW,CAACE,GAAG,CAAEC,IAAI,IAAK;MAC9C,MAAMG,GAAG,GAAG,GAAGH,IAAI,CAACzB,IAAI,IAAIyB,IAAI,CAACxB,KAAK,EAAE;MACxC,OAAOiC,YAAY,CAACQ,GAAG,CAACd,GAAG,CAAC,IAAI,CAAC;IACnC,CAAC,CAAC;;IAEF;IACA,MAAMiB,oBAAoB,GAAG;MAC3BC,MAAM,EAAE,CACN;QAAEC,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAE,MAAM;QAAEC,IAAI,EAAEd;MAAW,CAAC,EAClD;QAAEY,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAEV;MAAY,CAAC,EACtD;QAAEQ,IAAI,EAAE,oBAAoB;QAAEC,IAAI,EAAE,QAAQ;QAAEC,IAAI,EAAEZ;MAAS,CAAC,CAC/D;MACDa,KAAK,EAAE;QACLC,MAAM,EAAE,GAAG;QACXH,IAAI,EAAE,KAAK;QACXI,OAAO,EAAE,IAAI;QACbC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDC,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAAE;QAC5BC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAEC,IAAI,EAAE;UAC9B,IAAID,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,EAAE;UAE9D,MAAMI,MAAM,GAAGxD,IAAI,CAACI,GAAG,CAACgD,GAAG,CAAC;UAE5B,IAAII,MAAM,IAAI,IAAI,EAAE;YAClB,OAAO,GAAG,GAAG,CAACJ,GAAG,GAAG,IAAI,EAAE9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC5C,CAAC,MAAM,IAAIkD,MAAM,IAAI,CAAC,EAAE;YACtB,OAAO,GAAG,GAAGJ,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC,CAAC,MAAM;YACL,OAAO,GAAG,GAAG,CAAC8C,GAAG,GAAG,IAAI,EAAE9C,OAAO,CAAC,CAAC,CAAC;UACtC;QACF,CAAC;QACDmD,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC;UAAE;UACrCC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZd,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDa,UAAU,EAAE;UACVb,OAAO,EAAE;QACX;MACF,CAAC;MACDc,MAAM,EAAE;QACNC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,KAAK,EAAE;MACT,CAAC;MACDC,WAAW,EAAE;QACXC,GAAG,EAAE;UACHC,WAAW,EAAE,KAAK;UAClBpB,UAAU,EAAE;YACVqB,KAAK,EAAE;cACLpB,OAAO,EAAE,KAAK;cAAE;cAChBY,OAAO,EAAE,CAAC,EAAE;cACZJ,KAAK,EAAE;gBACLC,QAAQ,EAAE,MAAM;gBAChBE,UAAU,EAAE,KAAK;gBACjBU,KAAK,EAAE;cACT,CAAC;cACDnB,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;gBACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,IAAI;gBAEhE,MAAMI,MAAM,GAAGxD,IAAI,CAACI,GAAG,CAACgD,GAAG,CAAC;gBAE5B,IAAII,MAAM,IAAI,IAAI,EAAE;kBAClB,OAAO,GAAG,GAAG,CAACJ,GAAG,GAAG,IAAI,EAAE9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;gBAC5C,CAAC,MAAM,IAAIkD,MAAM,IAAI,CAAC,EAAE;kBACtB,OAAO,GAAG,GAAGJ,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;gBACnC,CAAC,MAAM;kBACL,OAAO,GAAG,GAAG,CAAC8C,GAAG,GAAG,IAAI,EAAE9C,OAAO,CAAC,CAAC,CAAC;gBACtC;cACF;YACF;UACF;QACF;MACF,CAAC;MACDiE,IAAI,EAAE;QACJC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAE;MACtB,CAAC;MACDC,MAAM,EAAE1D,UAAU;MAClB2D,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACfjB,QAAQ,EAAE,MAAM;QAChBkB,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLJ,IAAI,EAAE;QACR;MACF,CAAC;MACDK,KAAK,EAAE;QACLP,MAAM,EAAE;UACNhB,KAAK,EAAE;YACLE,MAAM,EAAE,MAAM;YACdD,QAAQ,EAAE;UACZ,CAAC;UACDG,OAAO,EAAE,EAAE,CAAE;QACf,CAAC;QACDoB,UAAU,EAAE;UACVnC,IAAI,EAAE;QACR,CAAC;QACDoC,SAAS,EAAE;UACTpC,IAAI,EAAE;QACR;MACF,CAAC;MACDqC,KAAK,EAAE;QACLrC,IAAI,EAAE,KAAK;QACX;QACAsC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,SAAAA,CAAA,EAAY;UACf;UACA,MAAMC,UAAU,GAAGtF,IAAI,CAACqF,GAAG,CAAC,GAAGxD,QAAQ,CAACb,GAAG,CAAC,CAACuE,IAAI,EAAE7E,CAAC,KAAK6E,IAAI,GAAGxD,WAAW,CAACrB,CAAC,CAAC,CAAC,CAAC;UAChF,MAAM8E,SAAS,GAAGxF,IAAI,CAACqF,GAAG,CAAC,GAAG1D,UAAU,CAAC;UACzC,OAAO3B,IAAI,CAACqF,GAAG,CAACC,UAAU,EAAEE,SAAS,CAAC,GAAG,GAAG;QAC9C;MACF,CAAC;MACD7B,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;MAAE;MAC3C8B,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClBC,eAAe,EAAE,QAAQ;QACzBjC,QAAQ,EAAE,MAAM;QAChBE,UAAU,EAAE,KAAK;QACjBc,OAAO,EAAE;UACPV,KAAK,EAAE,EAAE;UACTrB,MAAM,EAAE,EAAE;UACViD,MAAM,EAAE,CAAC,CAAE;QACb,CAAC;QACDnB,MAAM,EAAE;UACNd,MAAM,EAAE,MAAM;UACdkC,eAAe,EAAE;QACnB,CAAC;QACDC,UAAU,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,QAAQ,EAAE;QACZ,CAAC;QACDnC,OAAO,EAAE,EAAE;QACXoC,WAAW,EAAE;UACXC,gBAAgB,EAAE,IAAI,CAAE;QAC1B,CAAC;QACDC,WAAW,EAAE;UACXC,mBAAmB,EAAE,IAAI,CAAE;QAC7B;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE,KAAK;QAChB;QACAC,MAAM,EAAE,SAAAA,CAAU;UAAElE,MAAM;UAAEmE,WAAW;UAAEC,cAAc;UAAEC;QAAE,CAAC,EAAE;UAC5D,MAAMC,MAAM,GAAG,CAACtE,MAAM,CAAC,CAAC,CAAC,CAACoE,cAAc,CAAC,GAAG,IAAI,EAAEpG,OAAO,CAAC,CAAC,CAAC;UAC5D,MAAMuG,OAAO,GAAG,CAACvE,MAAM,CAAC,CAAC,CAAC,CAACoE,cAAc,CAAC,GAAG,IAAI,EAAEpG,OAAO,CAAC,CAAC,CAAC;UAC7D,MAAMiF,IAAI,GAAG,CAACjD,MAAM,CAAC,CAAC,CAAC,CAACoE,cAAc,CAAC,GAAG,IAAI,EAAEpG,OAAO,CAAC,CAAC,CAAC;UAC1D,MAAMwG,QAAQ,GAAGH,CAAC,CAACI,OAAO,CAACtC,MAAM,CAACiC,cAAc,CAAC;UAEjD,OAAO;AACjB;AACA,4EAA4EI,QAAQ;AACpF;AACA;AACA,0DAA0DF,MAAM;AAChE;AACA;AACA;AACA,2DAA2DC,OAAO;AAClE;AACA,gFAAgFtB,IAAI,IAAI,CAAC,GAAG,eAAe,GAAG,EAAE;AAChH;AACA,wDAAwDA,IAAI,aAAaA,IAAI,IAAI,CAAC,GAAG,WAAW,GAAG,EAAE;AACrG;AACA;AACA,oCAAoC,CAAClE,UAAU,CAACwF,OAAO,CAAC,GAAGxF,UAAU,CAACkE,IAAI,CAAC,EAAEjF,OAAO,CAAC,CAAC,CAAC;AACvF;AACA;AACA,OAAO;QACC;MACF,CAAC;MACD0G,IAAI,EAAE;QACJlE,IAAI,EAAE,KAAK;QACXmE,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE,EAAE,CAAE;QACd;MACF,CAAC;MACD;MACAC,WAAW,EAAE;QACXnC,KAAK,EAAEtD,QAAQ,CAAC0F,IAAI,CAACnE,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;UAC3CoE,CAAC,EAAE,CAAC;UACJC,WAAW,EAAE,SAAS;UACtBC,KAAK,EAAE;YACLD,WAAW,EAAE,SAAS;YACtBhE,KAAK,EAAE;cACLa,KAAK,EAAE,MAAM;cACbvB,UAAU,EAAE;YACd;UACF;QACF,CAAC;MACH;IACF,CAAC;;IAED;IACA,MAAM4E,gBAAgB,GAAG;MACvBrF,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,YAAY;QAClBE,IAAI,EAAEL;MACR,CAAC,CAAC;MACFM,KAAK,EAAE;QACLF,IAAI,EAAE,MAAM;QACZG,MAAM,EAAE,GAAG;QACXE,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE,aAAa;QACzB6E,IAAI,EAAE;UACJ3E,OAAO,EAAE;QACX;MACF,CAAC;MACDD,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;YACZ,OAAO,GAAG,GAAGA,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC,CAAC,MAAM,IAAI8C,GAAG,IAAI,GAAG,EAAE;YACrB,OAAO,GAAG,GAAGA,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC,CAAC,MAAM,IAAI8C,GAAG,IAAI,CAAC,GAAG,EAAE;YACtB,OAAO,IAAI,GAAGpD,IAAI,CAACI,GAAG,CAACgD,GAAG,CAAC,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC9C,CAAC,MAAM;YACL,OAAO,GAAG,GAAG8C,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC;QACF,CAAC;QACDmD,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZd,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDa,UAAU,EAAE;UACVb,OAAO,EAAE;QACX;MACF,CAAC;MACDc,MAAM,EAAE;QACNE,KAAK,EAAE,UAAU;QACjBD,KAAK,EAAE;MACT,CAAC;MACDO,IAAI,EAAE;QACJ/B,IAAI,EAAE;MACR,CAAC;MACDkC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdlB,MAAM,EAAEvB,aAAa,CAACpB,GAAG,CAACoC,GAAG,IAAIA,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC;QAClE2B,KAAK,EAAE;UACLJ,IAAI,EAAE;QACR,CAAC;QACDkD,QAAQ,EAAEzF,aAAa,CAACpB,GAAG,CAAC,CAACoC,GAAG,EAAE0E,KAAK,MAAM;UAC3CrB,WAAW,EAAE,CAAC;UACdC,cAAc,EAAEoB,KAAK;UACrBC,SAAS,EAAE3E,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;UAC3C4E,WAAW,EAAE,MAAM;UACnBrD,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC;MACDK,KAAK,EAAE;QACLjE,UAAU,EAAEA,UAAU;QACtB0D,MAAM,EAAE;UACNhB,KAAK,EAAE;YACLE,MAAM,EAAE,MAAM;YACdD,QAAQ,EAAE;UACZ;QACF,CAAC;QACDuB,UAAU,EAAE;UAAEnC,IAAI,EAAE;QAAM,CAAC;QAC3BoC,SAAS,EAAE;UAAEpC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDqC,KAAK,EAAE;QACLrC,IAAI,EAAE;MACR,CAAC;MACDa,MAAM,EAAE,CAAC,SAAS,CAAC;MAAE;MACrBO,WAAW,EAAE;QACX+D,IAAI,EAAE;UACJtE,MAAM,EAAE;YACNuE,SAAS,EAAE,CAAC;YACZC,mBAAmB,EAAE,SAAS;YAAE;YAChCC,mBAAmB,EAAE,SAAS,CAAG;UACnC;QACF;MACF,CAAC;MACD/B,OAAO,EAAE;QACPmB,CAAC,EAAE;UACDrE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;cACZ,OAAO,GAAG,GAAGA,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;YACpC,CAAC,MAAM,IAAI8C,GAAG,IAAI,GAAG,EAAE;cACrB,OAAO,GAAG,GAAGA,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;YACpC,CAAC,MAAM,IAAI8C,GAAG,IAAI,CAAC,GAAG,EAAE;cACtB,OAAO,IAAI,GAAGpD,IAAI,CAACI,GAAG,CAACgD,GAAG,CAAC,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;YAC/C,CAAC,MAAM;cACL,OAAO,GAAG,GAAG8C,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;YACpC;UACF;QACF;MACF,CAAC;MACD0G,IAAI,EAAE;QACJlE,IAAI,EAAE,KAAK;QACXmE,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,WAAW,EAAE;QACXnC,KAAK,EAAE,CAAC;UACNqC,CAAC,EAAE,CAAC;UACJC,WAAW,EAAE,MAAM;UACnBY,WAAW,EAAE,CAAC;UACdC,eAAe,EAAE,CAAC;UAClB9D,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC;;IAED;IACA,MAAM+D,kBAAkB,GAAG;MACzBjG,MAAM,EAAE,CAAC;QAAEC,IAAI,EAAE,qBAAqB;QAAEE,IAAI,EAAER;MAAsB,CAAC,CAAC;MACtES,KAAK,EAAE;QACLF,IAAI,EAAE,KAAK;QACXG,MAAM,EAAE,GAAG;QACXE,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDmB,WAAW,EAAE;QACXC,GAAG,EAAE;UACH4B,UAAU,EAAE,KAAK;UACjB3B,WAAW,EAAE,KAAK;UAClBoE,WAAW,EAAE,SAAS;UACtBxF,UAAU,EAAE;YAAE0C,QAAQ,EAAE;UAAM;QAChC;MACF,CAAC;MACD1C,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbyC,QAAQ,EAAE,KAAK;QACfvC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;YACZ,OAAOA,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B,CAAC,MAAM,IAAI8C,GAAG,IAAI,GAAG,EAAE;YACrB,OAAOA,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B,CAAC,MAAM,IAAI8C,GAAG,IAAI,CAAC,GAAG,EAAE;YACtB,OAAO,GAAG,GAAGpD,IAAI,CAACI,GAAG,CAACgD,GAAG,CAAC,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7C,CAAC,MAAM;YACL,OAAO8C,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B;QACF,CAAC;QACDuD,OAAO,EAAE,CAAC,EAAE;QACZJ,KAAK,EAAE;UAAEC,QAAQ,EAAE,MAAM;UAAEC,MAAM,EAAE,CAAC,MAAM,CAAC;UAAEC,UAAU,EAAE;QAAM;MACjE,CAAC;MACDG,MAAM,EAAE;QAAEjB,IAAI,EAAE,IAAI;QAAEkB,KAAK,EAAE,CAAC;QAAEL,MAAM,EAAE,CAAC,aAAa;MAAE,CAAC;MACzDqB,KAAK,EAAE;QACLjE,UAAU,EAAEA,UAAU;QACtB0D,MAAM,EAAE;UAAEhB,KAAK,EAAE;YAAEE,MAAM,EAAE,MAAM;YAAED,QAAQ,EAAE;UAAO;QAAE,CAAC;QACvDuB,UAAU,EAAE;UAAEnC,IAAI,EAAE;QAAM,CAAC;QAC3BoC,SAAS,EAAE;UAAEpC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDqC,KAAK,EAAE;QACLrC,IAAI,EAAE,KAAK;QACXsC,GAAG,EAAEpF,IAAI,CAACoF,GAAG,CAAC,GAAGnD,qBAAqB,CAAC,GAAG,CAAC,GAAGjC,IAAI,CAACoF,GAAG,CAAC,GAAGnD,qBAAqB,CAAC,GAAG,GAAG,GAAG,CAAC;QAC1FoD,GAAG,EAAErF,IAAI,CAACqF,GAAG,CAAC,GAAGpD,qBAAqB,CAAC,GAAG;MAC5C,CAAC;MACDsC,IAAI,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAC;MACpBb,MAAM,EAAE,CAAC,SAAS,CAAC;MACnB0C,OAAO,EAAE;QACPmB,CAAC,EAAE;UACDrE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,KAAK;YACjE,OAAOA,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B;QACF;MACF,CAAC;MACD0G,IAAI,EAAE;QACJlE,IAAI,EAAE,KAAK;QACXmE,OAAO,EAAE;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEC,GAAG,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAE;MACrD;IACF,CAAC;;IAED;IACA,MAAMoB,sBAAsB,GAAG;MAC7BnG,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,mBAAmB;QACzBE,IAAI,EAAEN;MACR,CAAC,CAAC;MACFO,KAAK,EAAE;QACLF,IAAI,EAAE,MAAM;QACZG,MAAM,EAAE,GAAG;QACXE,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE,aAAa;QACzB6E,IAAI,EAAE;UACJ3E,OAAO,EAAE;QACX;MACF,CAAC;MACDD,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;YACZ,OAAOA,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B,CAAC,MAAM,IAAI8C,GAAG,IAAI,GAAG,EAAE;YACrB,OAAOA,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B,CAAC,MAAM,IAAI8C,GAAG,IAAI,CAAC,GAAG,EAAE;YACtB,OAAO,GAAG,GAAGpD,IAAI,CAACI,GAAG,CAACgD,GAAG,CAAC,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7C,CAAC,MAAM;YACL,OAAO8C,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC7B;QACF,CAAC;QACDmD,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZd,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDa,UAAU,EAAE;UACVb,OAAO,EAAE;QACX;MACF,CAAC;MACDc,MAAM,EAAE;QACNE,KAAK,EAAE,UAAU;QACjBD,KAAK,EAAE;MACT,CAAC;MACDO,IAAI,EAAE;QACJ/B,IAAI,EAAE;MACR,CAAC;MACDkC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdlB,MAAM,EAAExB,kBAAkB,CAACnB,GAAG,CAACoC,GAAG,IAAIA,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC;QACvE2B,KAAK,EAAE;UACLJ,IAAI,EAAE;QACR,CAAC;QACDkD,QAAQ,EAAE1F,kBAAkB,CAACnB,GAAG,CAAC,CAACoC,GAAG,EAAE0E,KAAK,MAAM;UAChDrB,WAAW,EAAE,CAAC;UACdC,cAAc,EAAEoB,KAAK;UACrBC,SAAS,EAAE3E,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;UAC3C4E,WAAW,EAAE,MAAM;UACnBrD,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC;MACDK,KAAK,EAAE;QACLjE,UAAU,EAAEA,UAAU;QACtB0D,MAAM,EAAE;UACNhB,KAAK,EAAE;YACLE,MAAM,EAAE,MAAM;YACdD,QAAQ,EAAE;UACZ;QACF,CAAC;QACDuB,UAAU,EAAE;UAAEnC,IAAI,EAAE;QAAM,CAAC;QAC3BoC,SAAS,EAAE;UAAEpC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDqC,KAAK,EAAE;QACLrC,IAAI,EAAE;MACR,CAAC;MACDa,MAAM,EAAE,CAAC,SAAS,CAAC;MAAE;MACrBO,WAAW,EAAE;QACX+D,IAAI,EAAE;UACJtE,MAAM,EAAE;YACNuE,SAAS,EAAE,CAAC;YACZC,mBAAmB,EAAE,SAAS;YAC9BC,mBAAmB,EAAE;UACvB;QACF;MACF,CAAC;MACD/B,OAAO,EAAE;QACPmB,CAAC,EAAE;UACDrE,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;cACZ,OAAOA,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAC7B,CAAC,MAAM,IAAI8C,GAAG,IAAI,GAAG,EAAE;cACrB,OAAOA,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAC7B,CAAC,MAAM,IAAI8C,GAAG,IAAI,CAAC,GAAG,EAAE;cACtB,OAAO,GAAG,GAAGpD,IAAI,CAACI,GAAG,CAACgD,GAAG,CAAC,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAC7C,CAAC,MAAM;cACL,OAAO8C,GAAG,CAAC9C,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAC7B;UACF;QACF;MACF,CAAC;MACD0G,IAAI,EAAE;QACJlE,IAAI,EAAE,KAAK;QACXmE,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,WAAW,EAAE;QACXnC,KAAK,EAAE,CAAC;UACNqC,CAAC,EAAE,CAAC;UACJC,WAAW,EAAE,MAAM;UACnBY,WAAW,EAAE,CAAC;UACdC,eAAe,EAAE,CAAC;UAClB9D,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC;;IAED;IACA,CAACzG,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,kBAAkB,CAAC,CAACgB,OAAO,CAAEC,GAAG,IAAK;MACpF,IAAIA,GAAG,CAACC,OAAO,EAAE;QACfD,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,EAAE;MAC5B;IACF,CAAC,CAAC;;IAIF;IACA,MAAMqJ,aAAa,GAAGC,gBAAgB,CAAC,CAAC;;IAExC;IACAD,aAAa,CAACxJ,OAAO,CAACwD,KAAK,IAAI;MAC7B,QAAQA,KAAK,CAACtB,GAAG;QACf,KAAK,eAAe;UAClBsB,KAAK,CAACkG,OAAO,GAAGvG,oBAAoB;UACpCK,KAAK,CAACH,IAAI,GAAG,gBAAgB;UAC7B;QACF,KAAK,WAAW;UACdG,KAAK,CAACkG,OAAO,GAAGjB,gBAAgB;UAChCjF,KAAK,CAACH,IAAI,GAAG,YAAY;UACzB;QACF,KAAK,mBAAmB;UACtBG,KAAK,CAACkG,OAAO,GAAGL,kBAAkB;UAClC7F,KAAK,CAACH,IAAI,GAAG,qBAAqB;UAClC;QACF,KAAK,iBAAiB;UACpBG,KAAK,CAACkG,OAAO,GAAGH,sBAAsB;UACtC/F,KAAK,CAACH,IAAI,GAAG,mBAAmB;UAChC;MACJ;IACF,CAAC,CAAC;;IAEF;IACA,MAAMsG,mBAAmB,GAAGA,CAAC1J,GAAG,EAAEyJ,OAAO,EAAEE,SAAS,KAAK;MACvD,IAAI3J,GAAG,CAACC,OAAO,EAAE;QACf;QACAD,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,EAAE;;QAE1B;QACA0J,UAAU,CAAC,MAAM;UACf,IAAI5J,GAAG,CAACC,OAAO,EAAE;YACf,IAAI;cACF,MAAMsD,KAAK,GAAG,IAAIrF,UAAU,CAAC8B,GAAG,CAACC,OAAO,EAAEwJ,OAAO,CAAC;cAClDlG,KAAK,CAACsG,MAAM,CAAC,CAAC;;cAEd;cACA,IAAIF,SAAS,KAAK,gBAAgB,EAAE;gBAClCG,MAAM,CAACC,kBAAkB,GAAGxG,KAAK;cACnC,CAAC,MAAM,IAAIoG,SAAS,KAAK,YAAY,EAAE;gBACrCG,MAAM,CAACE,cAAc,GAAGzG,KAAK;cAC/B,CAAC,MAAM,IAAIoG,SAAS,KAAK,qBAAqB,EAAE;gBAC9CG,MAAM,CAACG,gBAAgB,GAAG1G,KAAK;cACjC,CAAC,MAAM,IAAIoG,SAAS,KAAK,mBAAmB,EAAE;gBAC5CG,MAAM,CAACI,oBAAoB,GAAG3G,KAAK;cACrC;YACF,CAAC,CAAC,OAAO4G,KAAK,EAAE;cACdC,OAAO,CAACD,KAAK,CAAC,gCAAgCR,SAAS,SAAS,EAAEQ,KAAK,CAAC;cACxE;cACAnK,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,kFAAkFyJ,SAAS,cAAc;YACnI;UACF;QACF,CAAC,EAAE,EAAE,CAAC;MACR;IACF,CAAC;;IAED;IACA,MAAMU,iBAAiB,GAAIC,SAAS,IAAK;MACvC,OAAOA,SAAS,IAAIA,SAAS,CAACjL,MAAM,GAAG,CAAC,IAAIiL,SAAS,CAAClC,IAAI,CAACnE,GAAG,IAAI/B,UAAU,CAAC+B,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1F,CAAC;;IAED;IACAsF,aAAa,CAACxJ,OAAO,CAAC,CAAC;MAAEC,GAAG;MAAEyJ,OAAO;MAAErG,IAAI;MAAEnB;IAAI,CAAC,KAAK;MACrD,IAAIjC,GAAG,CAACC,OAAO,EAAE;QACf,IAAIsK,OAAO,GAAG,KAAK;;QAEnB;QACA,IAAItI,GAAG,KAAK,eAAe,EAAE;UAC3B,MAAMN,WAAW,GAAG,CAAAlD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES,2BAA2B,KAAI,EAAE;UACjEqL,OAAO,GAAG5I,WAAW,CAACyG,IAAI,CAACtG,IAAI,IAAI;YACjC,MAAM2F,MAAM,GAAGvF,UAAU,CAACJ,IAAI,CAACW,WAAW,IAAI,CAAC,CAAC;YAChD,MAAM2D,IAAI,GAAGlE,UAAU,CAACJ,IAAI,CAACa,SAAS,IAAI,CAAC,CAAC;YAC5C,MAAM6H,QAAQ,GAAGtI,UAAU,CAACJ,IAAI,CAACe,aAAa,IAAI,CAAC,CAAC;YACpD,OAAO4E,MAAM,KAAK,CAAC,IAAIrB,IAAI,KAAK,CAAC,IAAIoE,QAAQ,KAAK,CAAC;UACrD,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIvI,GAAG,KAAK,WAAW,EAAE;UAAA,IAAAwI,qBAAA;UAC9B,MAAMxH,aAAa,GAAG,CAAAxE,UAAU,aAAVA,UAAU,wBAAAgM,qBAAA,GAAVhM,UAAU,CAAEkB,aAAa,cAAA8K,qBAAA,uBAAzBA,qBAAA,CAA2B5I,GAAG,CAACC,IAAI,IAAII,UAAU,CAACJ,IAAI,CAACnC,aAAa,IAAI,CAAC,CAAC,CAAC,KAAI,EAAE;UACvG4K,OAAO,GAAGF,iBAAiB,CAACpH,aAAa,CAAC;QAC5C,CAAC,MAAM,IAAIhB,GAAG,KAAK,mBAAmB,EAAE;UAAA,IAAAyI,qBAAA;UACtC,MAAMC,eAAe,GAAG,CAAAlM,UAAU,aAAVA,UAAU,wBAAAiM,qBAAA,GAAVjM,UAAU,CAAEc,wBAAwB,cAAAmL,qBAAA,uBAApCA,qBAAA,CAAsC7I,GAAG,CAACC,IAAI,IAAII,UAAU,CAACJ,IAAI,CAACO,mBAAmB,IAAI,CAAC,CAAC,CAAC,KAAI,EAAE;UAC1HkI,OAAO,GAAGF,iBAAiB,CAACM,eAAe,CAAC;QAC9C,CAAC,MAAM,IAAI1I,GAAG,KAAK,iBAAiB,EAAE;UAAA,IAAA2I,qBAAA;UACpC,MAAMC,aAAa,GAAG,CAAApM,UAAU,aAAVA,UAAU,wBAAAmM,qBAAA,GAAVnM,UAAU,CAAEgB,eAAe,cAAAmL,qBAAA,uBAA3BA,qBAAA,CAA6B/I,GAAG,CAACC,IAAI,IAAII,UAAU,CAACJ,IAAI,CAACrC,eAAe,IAAI,CAAC,CAAC,CAAC,KAAI,EAAE;UAC3G8K,OAAO,GAAGF,iBAAiB,CAACQ,aAAa,CAAC;QAC5C;QAEA,IAAIN,OAAO,EAAE;UACXb,mBAAmB,CAAC1J,GAAG,EAAEyJ,OAAO,EAAErG,IAAI,CAAC;QACzC,CAAC,MAAM;UACLpD,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,kFAAkFkD,IAAI,CAAC0H,WAAW,CAAC,CAAC,uBAAuB;QACrJ;MACF;IACF,CAAC,CAAC;;IAEF;IACA,CAAClM,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,kBAAkB,CAAC,CAACgB,OAAO,CAAEC,GAAG,IAAK;MACpF,IAAIA,GAAG,CAACC,OAAO,IAAI,CAACsJ,aAAa,CAACnB,IAAI,CAAC7E,KAAK,IAAIA,KAAK,CAACvD,GAAG,KAAKA,GAAG,CAAC,EAAE;QAClEA,GAAG,CAACC,OAAO,CAACC,SAAS,GAAG,EAAE;MAC5B;IACF,CAAC,CAAC;EACJ,CAAC;EAGD,MAAM6K,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,UAAU,KAAK;IACpD,MAAM1K,UAAU,GAAG,CACjB,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EACtD,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CACjE;IAED,IAAI,CAACyK,SAAS,IAAI,CAACC,UAAU,EAAE;MAC7B,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,MAAMC,cAAc,GAAG3K,UAAU,CAAC0K,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEnD,OAAO,GAAGC,cAAc,IAAIF,SAAS,EAAE;EACzC,CAAC;EAED,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAM7G,KAAK,GAAG;MAAE,GAAGhG;IAAgB,CAAC;IAEpC,IAAIgG,KAAK,CAACC,QAAQ,EAAE;MAClB,MAAMA,QAAQ,GAAG6G,QAAQ,CAAC9G,KAAK,CAACC,QAAQ,CAAC;MACzCD,KAAK,CAACC,QAAQ,GAAG,GAAGA,QAAQ,GAAG,CAAC,IAAI;IACtC;IACA,OAAOD,KAAK;EACd,CAAC;EAED,MAAM+G,iBAAiB,GAAIC,WAAW,IAAK;IACzC,IAAI,CAACA,WAAW,EAAE,OAAO,EAAE;IAE3B,IAAIA,WAAW,CAACjM,MAAM,GAAG,EAAE,EAAE;MAC3B,OAAOiM,WAAW,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;IAC7C;IAEA,OAAOD,WAAW;EACpB,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAAC/M,UAAU,CAACS,2BAA2B,EAAE,OAAO,CAAC,CAAC;IAEtD,OAAOT,UAAU,CAACS,2BAA2B,CAACuM,MAAM,CAClD,CAACC,MAAM,EAAEpL,KAAK,KAAK;MACjBoL,MAAM,CAACjJ,WAAW,IAAIP,UAAU,CAAC5B,KAAK,CAACmC,WAAW,IAAI,CAAC,CAAC;MACxDiJ,MAAM,CAAC/I,SAAS,IAAIT,UAAU,CAAC5B,KAAK,CAACqC,SAAS,IAAI,CAAC,CAAC;MACpD+I,MAAM,CAAC7I,aAAa,IAAIX,UAAU,CAAC5B,KAAK,CAACuC,aAAa,IAAI,CAAC,CAAC;MAC5D,OAAO6I,MAAM;IACf,CAAC,EACD;MAAEjJ,WAAW,EAAE,CAAC;MAAEE,SAAS,EAAE,CAAC;MAAEE,aAAa,EAAE;IAAE,CACnD,CAAC;EACH,CAAC;;EAED;EACA,MAAM2G,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMmC,SAAS,GAAG,CAChB;MACE1J,GAAG,EAAE,eAAe;MACpB2J,KAAK,EAAE,+BAA+B;MACtC5L,GAAG,EAAEpB,gBAAgB;MACrB6K,OAAO,EAAE,IAAI;MAAE;MACfc,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,EAAC9L,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAES,2BAA2B,GAAE,OAAO,KAAK;QAC1D,MAAMyC,WAAW,GAAGlD,UAAU,CAACS,2BAA2B;QAC1D,OAAOyC,WAAW,CAACyG,IAAI,CAACtG,IAAI,IAAI;UAC9B,MAAM2F,MAAM,GAAGvF,UAAU,CAACJ,IAAI,CAACW,WAAW,IAAI,CAAC,CAAC;UAChD,MAAM2D,IAAI,GAAGlE,UAAU,CAACJ,IAAI,CAACa,SAAS,IAAI,CAAC,CAAC;UAC5C,MAAM6H,QAAQ,GAAGtI,UAAU,CAACJ,IAAI,CAACe,aAAa,IAAI,CAAC,CAAC;UACpD,OAAO4E,MAAM,GAAG,CAAC,IAAIrB,IAAI,GAAG,CAAC,IAAIoE,QAAQ,GAAG,CAAC;QAC/C,CAAC,CAAC;MACJ;IACF,CAAC,EACD;MACEvI,GAAG,EAAE,WAAW;MAChB2J,KAAK,EAAE,mBAAmB;MAC1B5L,GAAG,EAAEnB,YAAY;MACjB4K,OAAO,EAAE,IAAI;MACbc,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,EAAC9L,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEkB,aAAa,GAAE,OAAO,KAAK;QAC5C,OAAOlB,UAAU,CAACkB,aAAa,CAACyI,IAAI,CAACtG,IAAI,IAAII,UAAU,CAACJ,IAAI,CAACnC,aAAa,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;MACzF;IACF,CAAC,EACD;MACEsC,GAAG,EAAE,mBAAmB;MACxB2J,KAAK,EAAE,qBAAqB;MAC5B5L,GAAG,EAAElB,cAAc;MACnB2K,OAAO,EAAE,IAAI;MACbc,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,EAAC9L,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEc,wBAAwB,GAAE,OAAO,KAAK;QACvD,OAAOd,UAAU,CAACc,wBAAwB,CAAC6I,IAAI,CAACtG,IAAI,IAAII,UAAU,CAACJ,IAAI,CAACO,mBAAmB,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;MAC1G,CAAC;MACDwJ,cAAc,EAAE,IAAI;MACpBC,WAAW,EAAE;QACXF,KAAK,EAAE,qBAAqB;QAC5BG,OAAO,EAAE;MACX;IACF,CAAC,EACD;MACE9J,GAAG,EAAE,iBAAiB;MACtB2J,KAAK,EAAE,mBAAmB;MAC1B5L,GAAG,EAAEjB,kBAAkB;MACvB0K,OAAO,EAAE,IAAI;MACbc,OAAO,EAAEA,CAAA,KAAM;QACb,IAAI,EAAC9L,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEgB,eAAe,GAAE,OAAO,KAAK;QAC9C,OAAOhB,UAAU,CAACgB,eAAe,CAAC2I,IAAI,CAACtG,IAAI,IAAII,UAAU,CAACJ,IAAI,CAACrC,eAAe,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;MAC7F,CAAC;MACDoM,cAAc,EAAE,IAAI;MACpBC,WAAW,EAAE;QACXF,KAAK,EAAE,mBAAmB;QAC1BG,OAAO,EAAE;MACX;IACF,CAAC,CACF;;IAED;IACA,OAAOJ,SAAS,CAACK,MAAM,CAACzI,KAAK,IAC3B3D,kBAAkB,CAAC2D,KAAK,CAACtB,GAAG,CAC9B,CAAC;EACH,CAAC;EAED,MAAMgK,SAAS,GAAGT,kBAAkB,CAAC,CAAC;EACtC;EACA,MAAMU,SAAS,GACbrL,IAAI,CAACC,KAAK,CACR,CAACmL,SAAS,CAACxJ,WAAW,GAAGwJ,SAAS,CAACtJ,SAAS,GAAGsJ,SAAS,CAACpJ,aAAa,IACtE,GACF,CAAC,GAAG,GAAG;;EAET;EACA,MAAM0G,aAAa,GAAGC,gBAAgB,CAAC,CAAC;;EAExC;EACA;EACA,MAAM2C,cAAc,GAAG5C,aAAa,CAAC9I,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChD,MAAM2L,cAAc,GAAG7C,aAAa,CAAC9I,KAAK,CAAC,CAAC,CAAC;;EAE7C;EACA,MAAM4L,WAAW,GAAI9I,KAAK,iBACxBnF,OAAA;IAAqBkO,SAAS,EAAC,8CAA8C;IAAAC,QAAA,gBAC3EnO,OAAA;MACEkO,SAAS,EAAC,2CAA2C;MACrDhI,KAAK,EAAE/F,mBAAoB;MAAAgO,QAAA,EAE1BhJ,KAAK,CAACqI;IAAK;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eACNvO,OAAA;MAAK4B,GAAG,EAAEuD,KAAK,CAACvD;IAAI;MAAAwM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAC1BpJ,KAAK,CAACsI,cAAc,iBACnBzN,OAAA;MAAKkO,SAAS,EAAC,4DAA4D;MAAAC,QAAA,gBACzEnO,OAAA;QACEkO,SAAS,EAAC,wBAAwB;QAClChI,KAAK,EAAE;UAAE,GAAG/F,mBAAmB;UAAEkG,UAAU,EAAE;QAAU,CAAE;QAAA8H,QAAA,EAExDhJ,KAAK,CAACuI,WAAW,CAACF;MAAK;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC,eACNvO,OAAA;QAAKkG,KAAK,EAAE9F,gBAAiB;QAAA+N,QAAA,EAC1BhJ,KAAK,CAACuI,WAAW,CAACC;MAAO;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,GApBOpJ,KAAK,CAACtB,GAAG;IAAAuK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAqBd,CACN;EAED,oBACEvO,OAAA;IAAKkO,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBnO,OAAA;MAAKkO,SAAS,EAAC,oEAAoE;MAAAC,QAAA,gBAEjFnO,OAAA;QAAKkO,SAAS,EAAC,2FAA2F;QAAAC,QAAA,gBACxGnO,OAAA;UACEkO,SAAS,EAAC,sCAAsC;UAChDhI,KAAK,EAAEhG,eAAgB;UAAAiO,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLvO,OAAA;UAAGkO,SAAS,EAAC,2BAA2B;UAAChI,KAAK,EAAE6G,iBAAiB,CAAC,CAAE;UAAAoB,QAAA,GACjExB,kBAAkB,CAACtM,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEmO,SAAS,EAAEnO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEoO,YAAY,CAAC,EAAC,KAAG,EAACxB,iBAAiB,CAAC5M,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE6M,WAAW,CAAC;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNvO,OAAA;QAAKkO,SAAS,EAAC,0EAA0E;QAAAC,QAAA,gBACvFnO,OAAA;UAAKkO,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BnO,OAAA;YAAKkO,SAAS,EAAC,cAAc;YAC3BhI,KAAK,EAAE;cAAE,GAAG9F,gBAAgB;cAAE+F,QAAQ,EAAE,MAAM;cAAEY,KAAK,EAAE;YAAU,CAAE;YAAAoH,QAAA,EACpE;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvBvO,OAAA;YAAKkO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClD7L,YAAY,CAACuL,SAAS,CAACxJ,WAAW;UAAC;YAAA+J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvO,OAAA;UACEkO,SAAS,EAAC,iBAAiB;UAC3BhI,KAAK,EAAE;YAAEwI,eAAe,EAAE;UAAU,CAAE;UAAAP,QAAA,gBAEtCnO,OAAA;YAAKkO,SAAS,EAAC,cAAc;YAC3BhI,KAAK,EAAE;cAAE,GAAG9F,gBAAgB;cAAE+F,QAAQ,EAAE,MAAM;cAAEY,KAAK,EAAE;YAAU,CAAE;YAAAoH,QAAA,EACpE;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7BvO,OAAA;YAAKkO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClD7L,YAAY,CAACuL,SAAS,CAACtJ,SAAS;UAAC;YAAA6J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvO,OAAA;UAAKkO,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BnO,OAAA;YAAKkO,SAAS,EAAC,cAAc;YAC3BhI,KAAK,EAAE;cAAE,GAAG9F,gBAAgB;cAAE+F,QAAQ,EAAE,MAAM;cAAEY,KAAK,EAAE;YAAU,CAAE;YAAAoH,QAAA,EACpE;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxBvO,OAAA;YAAKkO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClD7L,YAAY,CAACuL,SAAS,CAACpJ,aAAa;UAAC;YAAA2J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNvO,OAAA;UACEkO,SAAS,EAAC,iBAAiB;UAC3BhI,KAAK,EAAE;YAAEwI,eAAe,EAAE;UAAU,CAAE;UAAAP,QAAA,gBAEtCnO,OAAA;YAAKkO,SAAS,EAAC,cAAc;YAC3BhI,KAAK,EAAE;cAAE,GAAG9F,gBAAgB;cAAE+F,QAAQ,EAAE,MAAM;cAAEY,KAAK,EAAE;YAAU,CAAE;YAAAoH,QAAA,EACpE;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBvO,OAAA;YAAKkO,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClD7L,YAAY,CAACwL,SAAS;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLR,cAAc,CAACtK,GAAG,CAAC0B,KAAK,IAAI8I,WAAW,CAAC9I,KAAK,CAAC,CAAC;IAAA;MAAAiJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAE7C,CAAC,EAGLP,cAAc,CAAC/M,MAAM,GAAG,CAAC,iBACxBjB,OAAA;MAAKkO,SAAS,EAAC,+DAA+D;MAAAC,QAAA,gBAC5EnO,OAAA;QAAKkO,SAAS,EAAC,oGAAoG;QAAAC,QAAA,gBACjHnO,OAAA;UACEkO,SAAS,EAAC,sCAAsC;UAChDhI,KAAK,EAAEhG,eAAgB;UAAAiO,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLvO,OAAA;UAAGkO,SAAS,EAAC,2BAA2B;UAAChI,KAAK,EAAE6G,iBAAiB,CAAC,CAAE;UAAAoB,QAAA,GACjExB,kBAAkB,CAACtM,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEmO,SAAS,EAAEnO,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEoO,YAAY,CAAC,EAAC,KAAG,EAACxB,iBAAiB,CAAC5M,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE6M,WAAW,CAAC;QAAA;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGLP,cAAc,CAACvK,GAAG,CAAC0B,KAAK,IAAI8I,WAAW,CAAC9I,KAAK,CAAC,CAAC;IAAA;MAAAiJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChO,EAAA,CA1hCIN,mBAAmB;AAAA0O,EAAA,GAAnB1O,mBAAmB;AA4hCzB,eAAeA,mBAAmB;AAAC,IAAA0O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}