// import coverImage from './../../../assets/cover.png';
// import logo from './../../../assets/LogoNew.png';
// import qboButton from "./../../../assets/C2QB_green_btn_med_default.svg";
// import coverImagee from "./../../../assets/logos/Top-Elements-v2.svg";

const DeepSightCoverPage = ({
  reportData = null
}) => {

  const formatHeaderPeriod = (startYear, startMonth) => {
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    if (!startYear || !startMonth) {
      return " "; 
    }

    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index

    return `${startMonthName} ${startYear}`;
  };

  const formatCompanyName = (companyName) => {
    if (!companyName) return '';
    
    if (companyName.length > 15) {
      return companyName.substring(0, 15) + '...';
    }
    
    return companyName;
  };

  return (
    <div className=" p-5">
      {/* Main Container */}
      <div className="max-w-6xl mx-auto bg-white flex flex-col min-h-screen h-[400mm]">



        {/* Main Content Area */}
        <div className='flex flex-col justify-center h-full relative'>

          {/* Top Section with Current Financial State Analysis */}
          <div className="relative mb-12">
            <img src="https://valisight-dev.s3.us-east-1.amazonaws.com/top_image.png" alt="Cover Page" className="w-full h-auto" />
          </div>

          {/* Middle Content Section */}
          <div className="pl-24 mb-24">
            <div className="mb-8">
              <p
                className="text-3xl font-light text-gray-600 mb-2 font-medium font-open-sans"
              >
                Prepared For:
              </p>
              <h2
                className="text-8xl font-bold text-gray-800 mb-2 font-open-sans"
                title={reportData?.companyName} // Show full name on hover
              >
                {formatCompanyName(reportData?.companyName)}
              </h2>
            </div>

            <div>
              <p
                className="text-6xl w-1/2 pb-6 font-semibold text-[#1E7C8C] border-b border-gray-800 font-open-sans"
              >
               {formatHeaderPeriod(reportData?.FYEndYear, reportData?.FYStartMonth)}
              </p>
            </div>
          </div>

          {/* Bottom Section with Logo */}
          <div className='flex justify-end pr-24  mt-20'>
            <div className=" mt-auto flex flex-col justify-end h-1/4">
              <div className="">
                <p
                  className="text-3xl text-right mb-2 font-open-sans"
                >
                  Prepared By:
                </p>
              </div>
              <div className='flex items-center text-right'>
                <img src="https://valisight-dev.s3.us-east-1.amazonaws.com/logo.png" alt="Logo" width={200} />
                <p className='text-6xl text-gray-800 font-bold font-open-sans' style={{ marginLeft: '-50px' }}>VALISIGHTS</p>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default DeepSightCoverPage;