import React from 'react';

const TableOfContents = ({
  headerTextStyle = {},
  headingTextStyle = {},
  contentTextStyle = {},
  subHeadingTextStyle = {},
  reportData = null,
  contentSettings = null
}) => {
  // Function to check if a chart should be displayed based on content settings
  const shouldDisplayChart = (chartKey) => {
    if (!contentSettings?.chartSettings) return true; // Default to true if no settings
    return contentSettings.chartSettings[chartKey] === true;
  };

  // Function to calculate dynamic TOC items based on enabled charts
  const calculateDynamicTocItems = () => {
    const tocItems = [];
    let currentPageNumber = 1;

    // 1. Report Summary - Always included (3 pages)
    tocItems.push({
      text: "Report Summary",
      page: currentPageNumber
    });
    currentPageNumber += 3;

    // 2. Fiscal Year Component
    const fiscalYearCharts = ['incomeSummary', 'netIncome', 'grossProfitMargin', 'netProfitMargin'];
    const enabledFiscalYearCharts = fiscalYearCharts.filter(chart => shouldDisplayChart(chart));

    // Fiscal Year always shows (even if all charts disabled) - 1 or 2 pages based on enabled charts
    let fiscalYearPages = 1;
    if (enabledFiscalYearCharts.length >= 3) {
      fiscalYearPages = 2;
    }

    tocItems.push({
      text: "Current Fiscal Year",
      page: currentPageNumber
    });
    currentPageNumber += fiscalYearPages;

    // 3. Expense Summary Component
    const expenseSummaryCharts = ['roaAndRoe', 'expensesTopAccounts', 'expensesTopAccountsMonthly', 'expensesWagesVsRevenueMonthly'];
    const enabledExpenseSummaryCharts = expenseSummaryCharts.filter(chart => shouldDisplayChart(chart));

    // Only include if at least one chart is enabled
    if (enabledExpenseSummaryCharts.length > 0) {
      let expenseSummaryPages = 1;
      if (enabledExpenseSummaryCharts.length >= 3) {
        expenseSummaryPages = 2;
      }

      tocItems.push({
        text: "Expense Summary",
        page: currentPageNumber
      });
      currentPageNumber += expenseSummaryPages;
    }

    // 4. Operational Efficiency Component
    const operationalEfficiencyCharts = ['daysSalesOutstanding', 'daysPayablesOutstanding', 'daysInventoryOutstanding', 'cashConversionCycle', 'fixedAssetTurnover'];
    const enabledOperationalEfficiencyCharts = operationalEfficiencyCharts.filter(chart => shouldDisplayChart(chart));

    // Only include if at least one chart is enabled
    if (enabledOperationalEfficiencyCharts.length > 0) {
      let operationalEfficiencyPages = 1;
      if (enabledOperationalEfficiencyCharts.length >= 4) {
        operationalEfficiencyPages = 2;
      }

      tocItems.push({
        text: "Operational Efficiency",
        page: currentPageNumber
      });
      currentPageNumber += operationalEfficiencyPages;
    }

    // 5. Liquidity Summary Component
    const liquiditySummaryCharts = ['netChangeInCash', 'quickRatio', 'monthsCashOnHand'];
    const enabledLiquiditySummaryCharts = liquiditySummaryCharts.filter(chart => shouldDisplayChart(chart));

    // Only include if at least one chart is enabled
    if (enabledLiquiditySummaryCharts.length > 0) {
      tocItems.push({
        text: "Liquidity Summary",
        page: currentPageNumber
      });
      currentPageNumber += 1;
    }

    // 6. Profit and Loss Reports
    const plReports = ['thirteenMonthTrailing', 'monthly', 'ytd'];
    const enabledPLReports = plReports.filter(report => shouldDisplayChart(report));

    // Only include if at least one P&L report is enabled
    if (enabledPLReports.length > 0) {
      // Add individual P&L reports that are enabled
      if (shouldDisplayChart('thirteenMonthTrailing')) {
        tocItems.push({
          text: "Profit & Loss - 13 Month Trailing",
          page: currentPageNumber
        });
        currentPageNumber += 4;
      }

      if (shouldDisplayChart('monthly')) {
        tocItems.push({
          text: "Profit & Loss - Monthly",
          page: currentPageNumber
        });
        currentPageNumber += 3;
      }

      if (shouldDisplayChart('ytd')) {
        tocItems.push({
          text: "Profit & Loss - YTD",
          page: currentPageNumber
        });
        currentPageNumber += 2;
      }
    }

    // 7. Balance Sheet
    if (shouldDisplayChart('balanceSheet')) {
      tocItems.push({
        text: "Balance Sheet",
        page: currentPageNumber
      });
      currentPageNumber += 3;
    }

    return tocItems;
  };

  // Get dynamic TOC items based on enabled charts
  const tocItems = calculateDynamicTocItems();

  const formatHeaderPeriod = (startYear, startMonth) => {
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    if (!startYear || !startMonth) {
      return " "; // fallback
    }

    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index

    return `${startMonthName} ${startYear}`;
  };

  const formatCompanyName = (companyName) => {
    if (!companyName) return '';
    
    if (companyName.length > 15) {
      return companyName.substring(0, 15) + '...';
    }
    
    return companyName;
  };

  const formatHeaderStyle = () => {
    const style = { ...headerTextStyle };
    if (style.fontSize) {
      const fontSize = parseInt(style.fontSize);
      style.fontSize = `${fontSize / 2}px`;
    }
    return style;
  };


  return (
    <div className="p-5">

      {/* Main Container */}
      <div className="max-w-6xl h-[400mm] mx-auto bg-white flex flex-col min-h-screen p-10  h-[297mm]">

         {/* Header Section */}
    <div className="component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2">
          <h1
            className="text-4xl font-bold text-gray-800 m-0"
            style={headerTextStyle}
          >
          </h1>
          <p className="text-lg text-gray-600 m-0" style={formatHeaderStyle()}>
            {formatHeaderPeriod(reportData?.FYEndYear, reportData?.FYStartMonth)} | {formatCompanyName(reportData?.companyName)}
          </p>
        </div>
        
        <div className='flex flex-col justify-center h-full'>

        {/* Header Section */}
        <div className="text-center flex justify-center   pb-6">
          <h1 
            className="text-5xl font-light text-gray-800 m-0"
            style={headingTextStyle}
          >
            Table of Contents
          </h1>
        </div>

       {/* Table of Contents Items */}
        <div className="max-w-2xl mx-auto w-full px-20 space-y-1">
          {tocItems.map((item, index) => (
            <div 
              key={index}
              className={`
                flex items-baseline	 py-2 px-2
                ${index === tocItems.length - 1 ? '' : ''}
              `}
            >
              <span 
                className="text-lg text-gray-700 font-medium flex-shrink-0"
                style={contentTextStyle}
              >
                {item.text}
              </span>
              <div className="flex-1 mx-4 border-b-[2.5px] border-dotted border-black min-w-4"></div>
              <span 
                className="text-lg text-teal-600 font-semibold flex-shrink-0"
                style={contentTextStyle}
              >
                {item.page}
              </span>
            </div>
          ))}
        </div>
        </div>

      </div>
    </div>
  );
};

export default TableOfContents;